# 更新日志

本文件记录了World Tune项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划新增
- 真实电台API集成
- 个性化推荐算法
- 播放列表功能
- 离线下载支持
- 社交分享功能
- 用户账户系统

## [1.0.0] - 2025-01-08

### 新增
- 🎵 **核心播放功能**
  - 支持MP3、M3U8格式的网络电台播放
  - 全局迷你播放器
  - 全屏播放器界面
  - 播放/暂停控制

- 📱 **用户界面**
  - Material Design 3设计风格
  - 底部导航栏（首页、探索、资料库）
  - 响应式布局设计
  - 明暗主题支持

- 🏠 **首页功能**
  - 推荐电台展示
  - 热门电台列表
  - 搜索入口

- 🔍 **探索页面**
  - 电台分类浏览（音乐、新闻、体育等）
  - 水平滑动电台列表
  - 分类筛选功能

- 📚 **资料库功能**
  - 收藏电台管理
  - 播放历史记录
  - Tab切换界面

- 🔍 **搜索功能**
  - 全局电台搜索
  - 热门分类快速筛选
  - 实时搜索结果

- ⭐ **收藏系统**
  - 本地收藏存储
  - 添加/移除收藏
  - 收藏状态同步

- 📊 **播放历史**
  - 自动记录播放历史
  - 最多保存50条记录
  - 历史记录管理

- 🌍 **国际化支持**
  - 中英文双语支持
  - 动态语言切换
  - 完整的翻译覆盖

- 🏗️ **技术架构**
  - Flutter 3.0+ 跨平台框架
  - Riverpod状态管理
  - go_router路由管理
  - just_audio音频播放
  - SharedPreferences本地存储

- 📱 **平台支持**
  - iOS 12.0+
  - Android 5.0+
  - Web浏览器

### 技术实现
- **模块化架构**: 按功能模块组织代码结构
- **状态管理**: 使用Riverpod进行高效状态管理
- **音频播放**: 集成just_audio支持多种音频格式
- **本地存储**: 使用SharedPreferences存储用户数据
- **网络请求**: 配置Dio进行API调用
- **代码生成**: 使用build_runner和json_serializable

### 数据库设计
- **MySQL数据库**: 设计9张核心表
- **表前缀**: 统一使用world_tune_前缀
- **数据模型**: 完整的电台、用户、收藏、历史数据模型
- **关系设计**: 合理的外键关系和索引优化

### API设计
- **RESTful API**: 完整的API接口设计
- **电台接口**: 列表、详情、搜索、分类API
- **用户接口**: 登录、个人信息、收藏、历史API
- **认证系统**: Firebase JWT认证支持

### 开发工具
- **iOS配置**: 完整的Xcode项目配置
- **权限配置**: 音频播放和网络权限设置
- **构建脚本**: iOS快速启动脚本
- **文档完善**: 详细的开发和部署文档

### 文档
- **项目文档**: 完整的README和功能说明
- **数据库文档**: 详细的数据库设计文档
- **API文档**: 完整的API接口文档
- **部署文档**: iOS配置和部署指南
- **模块文档**: 各功能模块的详细说明

## [0.1.0] - 2025-01-01

### 新增
- 项目初始化
- 基础架构搭建
- Flutter环境配置

---

## 版本说明

### 版本号格式
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增**: 新功能
- **更改**: 对现有功能的变更
- **弃用**: 不久将移除的功能
- **移除**: 已移除的功能
- **修复**: 任何bug修复
- **安全**: 安全相关的修复

### 发布周期
- **主版本**: 每年1-2次重大更新
- **次版本**: 每月1-2次功能更新
- **修订版本**: 根据需要发布bug修复

### 支持政策
- **当前版本**: 完全支持和维护
- **前一版本**: 安全更新和重要bug修复
- **更早版本**: 不再维护，建议升级
