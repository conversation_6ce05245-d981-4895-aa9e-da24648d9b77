import 'dart:async';
import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:world_tune/src/shared/providers/startup_cache_provider.dart';
import 'package:world_tune/src/shared/services/startup_cache_service.dart';
import 'package:world_tune/src/shared/services/storage_service.dart';
import 'package:world_tune/src/shared/utils/logger.dart';
import 'package:world_tune/src/shared/utils/platform_type.dart';

import 'package:world_tune/app.dart';

Future<void> start() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  FlutterError.onError = (details) {
    log(details.exceptionAsString(), stackTrace: details.stack);
  };

  final platformType = detectPlatformType();

  // 初始化StorageService
  final storageService = await StorageService.create();

  // 初始化StartupCacheService
  final startupCacheService = StartupCacheService();
  await startupCacheService.initialize();

  runApp(EasyLocalization(
    supportedLocales: const [Locale('en'), Locale('zh')],
    path: 'assets/lang',
    fallbackLocale: const Locale('en'),
    child: ProviderScope(
      overrides: [
        platformTypeProvider.overrideWithValue(platformType),
        storageServiceProvider.overrideWithValue(storageService),
        startupCacheServiceProvider.overrideWithValue(startupCacheService),
      ],
      observers: [
        Logger(),
      ],
      child: const App(),
    ),
  ));
}
