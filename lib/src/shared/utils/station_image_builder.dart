import 'package:flutter/material.dart';
import '../models/models.dart';
import 'backup_radio_images.dart';

/// 电台图片构建工具类
/// 统一处理电台图片加载、备用图片显示和错误处理
class StationImageBuilder {
  /// 构建电台图片Widget
  /// 
  /// [station] 电台信息
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [fit] 图片填充方式
  /// [borderRadius] 圆角半径
  static Widget buildStationImage({
    required StationSimple station,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    Widget imageWidget;

    if (_isValidImageUrl(station.favicon)) {
      // 有有效favicon的电台，尝试加载网络图片，失败时使用备用图片
      imageWidget = Image.network(
        station.favicon,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            return child;
          }
          // 显示简洁的加载指示器
          return Container(
            width: width,
            height: height,
            color: Colors.grey.shade200,
            child: Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.grey.shade400,
                ),
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          // print('📷 电台${station.name}的favicon加载失败，使用备用图片');
          return _buildBackupImage(station, width, height, fit);
        },
        headers: _getImageHeaders(station.favicon),
      );
    } else {
      // 没有favicon或URL无效的电台，直接使用备用图片
      // print('🚫 电台${station.name}的favicon无效或为空: "${station.favicon}"');
      imageWidget = _buildBackupImage(station, width, height, fit);
    }

    // 如果指定了圆角，包装在ClipRRect中
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 构建备用图片
  static Widget _buildBackupImage(StationSimple station, double? width, double? height, BoxFit fit) {
    final backupImagePath = BackupRadioImages.getRandomBackupImage(station.id);
    return Image.asset(
      backupImagePath,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        // 如果连备用图片都加载失败，则使用渐变占位图
        print('⚠️ 备用图片${backupImagePath}也加载失败，使用占位图');
        return _buildPlaceholder(width, height);
      },
    );
  }

  /// 构建渐变占位图
  static Widget _buildPlaceholder(double? width, double? height) {
    // 随机生成不同的渐变色彩
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600, Colors.blue.shade800],
      [Colors.green.shade400, Colors.green.shade600, Colors.green.shade800],
      [Colors.purple.shade400, Colors.purple.shade600, Colors.purple.shade800],
      [Colors.orange.shade400, Colors.orange.shade600, Colors.orange.shade800],
      [Colors.teal.shade400, Colors.teal.shade600, Colors.teal.shade800],
      [Colors.indigo.shade400, Colors.indigo.shade600, Colors.indigo.shade800],
    ];
    
    final colorSet = colors[DateTime.now().millisecond % colors.length];
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: colorSet,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.radio,
              color: Colors.white.withOpacity(0.9),
              size: (width != null && width < 60) ? 20 : 28,
            ),
            if (width == null || width >= 60) ...[
              const SizedBox(height: 4),
              Text(
                'FM',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: (width != null && width < 60) ? 8 : 10,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 验证图片URL是否有效
  static bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url.trim().isEmpty) {
      return false;
    }
    
    // 检查URL格式
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }
    
    // 检查是否包含常见的无效模式
    final invalidPatterns = [
      'duckduckgo.com/ip3/', // DuckDuckGo代理的图片经常有问题
      'data:image/', // Base64图片数据（通常无法正常显示）
    ];
    
    for (final pattern in invalidPatterns) {
      if (url.contains(pattern)) {
        // print('⚠️ 检测到可能有问题的图片URL模式: $pattern');
        return false;
      }
    }
    
    return true;
  }

  /// 为复杂API图片URL生成合适的HTTP头
  static Map<String, String> _getImageHeaders(String url) {
    final headers = <String, String>{
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Cache-Control': 'max-age=31536000',
    };
    
    // 对于特定域名添加专门的头信息
    if (url.contains('iheart.com')) {
      headers['Referer'] = 'https://www.iheart.com/';
      headers['Origin'] = 'https://www.iheart.com';
      // iHeartRadio可能需要特定的User-Agent
      headers['User-Agent'] = 'Mozilla/5.0 (compatible; WorldTuneApp/1.0; +https://worldtune.app)';
    }
    
    return headers;
  }
} 