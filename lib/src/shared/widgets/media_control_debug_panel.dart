import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/models.dart';
import '../services/audio_service.dart';
import '../services/media_control_test_helper.dart';
import '../services/media_control_initializer.dart';

/// 媒体控制调试面板
/// 
/// 提供测试锁屏播放器功能的调试界面
/// 功能实现: 可视化的媒体控制测试工具
/// 实现方案: 提供按钮触发各种测试场景
/// 影响范围: 开发和调试阶段
/// 实现日期: 2025-01-27
class MediaControlDebugPanel extends ConsumerWidget {
  const MediaControlDebugPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentStation = ref.watch(currentStationProvider);
    final playlistContext = ref.watch(currentPlaylistProvider);
    final playbackAsync = ref.watch(currentPlaybackProvider);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.bug_report, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  '锁屏播放器调试面板',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 状态信息
            _buildStatusSection(context, currentStation, playlistContext, playbackAsync),
            
            const SizedBox(height: 16),

            // 测试按钮
            _buildTestButtons(context, ref),
            
            const SizedBox(height: 16),

            // 快速操作
            _buildQuickActions(context, ref),
          ],
        ),
      ),
    );
  }

  /// 构建状态信息部分
  Widget _buildStatusSection(
    BuildContext context,
    StationSimple? currentStation,
    PlaylistContext? playlistContext,
    AsyncValue<CurrentPlayback> playbackAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '当前状态',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        _buildStatusItem('媒体控制初始化', MediaControlInitializer.isInitialized ? '已初始化' : '未初始化'),
        _buildStatusItem('当前电台', currentStation?.name ?? '无'),
        _buildStatusItem('播放列表', playlistContext?.sourceTitle ?? '无'),
        
        if (playlistContext != null) ...[
          _buildStatusItem('列表位置', '${playlistContext.currentIndex + 1}/${playlistContext.stations.length}'),
          _buildStatusItem('可播放上一首', playlistContext.canPlayPrevious ? '是' : '否'),
          _buildStatusItem('可播放下一首', playlistContext.canPlayNext ? '是' : '否'),
        ],
        
        playbackAsync.when(
          data: (playback) => _buildStatusItem('播放状态', playback.state.name),
          loading: () => _buildStatusItem('播放状态', '加载中...'),
          error: (error, _) => _buildStatusItem('播放状态', '错误: $error'),
        ),
      ],
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建测试按钮部分
  Widget _buildTestButtons(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '功能测试',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: () => _runBasicTest(ref),
              icon: const Icon(Icons.play_arrow, size: 16),
              label: const Text('基础播放测试'),
            ),
            ElevatedButton.icon(
              onPressed: () => _runPlaylistTest(ref),
              icon: const Icon(Icons.playlist_play, size: 16),
              label: const Text('播放列表测试'),
            ),
            ElevatedButton.icon(
              onPressed: () => _runFullTest(ref),
              icon: const Icon(Icons.science, size: 16),
              label: const Text('完整测试套件'),
            ),
            ElevatedButton.icon(
              onPressed: () => _printStatus(ref),
              icon: const Icon(Icons.info, size: 16),
              label: const Text('打印状态'),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建快速操作部分
  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速操作',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            OutlinedButton.icon(
              onPressed: () => _createTestPlaylist(ref),
              icon: const Icon(Icons.queue_music, size: 16),
              label: const Text('创建测试列表'),
            ),
            OutlinedButton.icon(
              onPressed: () => _clearPlaylist(ref),
              icon: const Icon(Icons.clear_all, size: 16),
              label: const Text('清除播放列表'),
            ),
            OutlinedButton.icon(
              onPressed: () => _stopPlayback(ref),
              icon: const Icon(Icons.stop, size: 16),
              label: const Text('停止播放'),
            ),
          ],
        ),
      ],
    );
  }

  /// 运行基础播放测试
  Future<void> _runBasicTest(WidgetRef ref) async {
    await MediaControlTestHelper.testBasicPlaybackControl(ref);
  }

  /// 运行播放列表测试
  Future<void> _runPlaylistTest(WidgetRef ref) async {
    await MediaControlTestHelper.testPlaylistControl(ref);
  }

  /// 运行完整测试套件
  Future<void> _runFullTest(WidgetRef ref) async {
    await MediaControlTestHelper.runFullTestSuite(ref);
  }

  /// 打印当前状态
  Future<void> _printStatus(WidgetRef ref) async {
    await MediaControlTestHelper.printCurrentStatus(ref);
  }

  /// 创建测试播放列表
  Future<void> _createTestPlaylist(WidgetRef ref) async {
    final testPlaylist = MediaControlTestHelper.createTestPlaylist();
    final audioService = ref.read(audioServiceProvider);
    
    await audioService.playStationWithPlaylist(
      station: testPlaylist.currentStation,
      playlistContext: testPlaylist,
    );
  }

  /// 清除播放列表
  void _clearPlaylist(WidgetRef ref) {
    ref.read(currentPlaylistProvider.notifier).state = null;
  }

  /// 停止播放
  Future<void> _stopPlayback(WidgetRef ref) async {
    final audioService = ref.read(audioServiceProvider);
    await audioService.stop();
  }
}
