import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/models.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';
import '../utils/text_overflow_handler.dart';
import '../utils/station_image_builder.dart';
import 'audio_visualizer.dart';

/// 播放器Modal组件 - 从MiniPlayer展开的全屏播放器
class PlayerModal extends ConsumerStatefulWidget {
  const PlayerModal({
    super.key,
    required this.onClose,
    required this.animation,
  });

  final VoidCallback onClose;
  final Animation<double> animation;

  @override
  ConsumerState<PlayerModal> createState() => _PlayerModalState();
}

class _PlayerModalState extends ConsumerState<PlayerModal>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;

  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeCarousel();
    _startEntryAnimation();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  void _startEntryAnimation() async {
    await Future<void>.delayed(const Duration(milliseconds: 100));
    if (mounted) {
      _fadeController.forward();
      _slideController.forward();
      await Future<void>.delayed(const Duration(milliseconds: 150));
      if (mounted) {
        _scaleController.forward();
      }
    }
  }

  late PageController _pageController;
  int _currentCarouselIndex = 0; // 默认显示第一张图片
  bool _isChangingStation = false; // 防止重复切换

  /// 初始化轮播图控制器
  void _initializeCarousel() {
    _pageController = PageController(
      initialPage: 0, // 从第一张开始，具体位置在构建时确定
      viewportFraction: 0.75, // 显示左右两边的部分图片，增强滑动感
    );
  }

  /// 重置轮播图到中心位置
  /// 
  /// 功能实现: 平滑回弹到中心位置，避免晃动
  /// 实现方案: 即时执行动画，提供丝滑体验
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _resetToCenter() {
    if (mounted && _pageController.hasClients) {
      _currentCarouselIndex = 1;
      _pageController.animateToPage(
        1,
        duration: const Duration(milliseconds: 150),
        curve: Curves.fastOutSlowIn,
      );
    }
  }

  /// 重置轮播图到当前电台位置
  /// 
  /// 功能实现: 根据实际电台列表，回弹到当前电台的正确位置
  /// 实现方案: 动态计算当前电台在实际列表中的索引
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _resetToCurrentCenter(List<StationSimple> actualStations, StationSimple currentStation) {
    if (mounted && _pageController.hasClients) {
      final currentStationIndex = actualStations.indexWhere((s) => s.id == currentStation.id);
      if (currentStationIndex != -1) {
        _currentCarouselIndex = currentStationIndex;
        _pageController.animateToPage(
          currentStationIndex,
          duration: const Duration(milliseconds: 150),
          curve: Curves.fastOutSlowIn,
        );
      }
    }
  }

  /// 播放上一首
  /// 
  /// 功能实现: 在当前播放列表中切换到上一首电台
  /// 实现方案: 调用AudioService的playPrevious方法
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Future<void> _playPrevious() async {
    print('⏮️ PlayerModal: 尝试播放上一首');
    
    final audioService = ref.read(audioServiceProvider);
    final success = await audioService.playPrevious();
    
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Already the first station'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  /// 播放下一首
  /// 
  /// 功能实现: 在当前播放列表中切换到下一首电台
  /// 实现方案: 调用AudioService的playNext方法
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Future<void> _playNext() async {
    print('⏭️ PlayerModal: 尝试播放下一首');
    
    final audioService = ref.read(audioServiceProvider);
    final success = await audioService.playNext();
    
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Already the last station'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentStation = ref.watch(currentStationProvider);
    final playbackAsync = ref.watch(currentPlaybackProvider);
    final favoritesAsync = ref.watch(favoriteStationsProvider);

    if (currentStation == null) {
      return Material(
        color: Colors.grey.shade50,
        child: SafeArea(
          child: Column(
            children: [
              _buildTopNavigation(context),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.radio,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      TextOverflowHandler.safeText(
                        'no_data'.tr(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Stack(
              children: [
                // 背景
                _buildBackground(context, currentStation),
                
                // 主要内容
                SafeArea(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      children: [
                        // 顶部导航
                        _buildTopNavigation(context),
                        
                        // 主要内容区域
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: Column(
                            children: [
                              const SizedBox(height: 20),
                              
                              // 轮播图式电台图片展示区域
                              Expanded(
                                flex: 6,
                                child: ScaleTransition(
                                  scale: _scaleAnimation,
                                  child: _buildStationCarousel(context, ref, currentStation),
                                ),
                              ),
                                
                                const SizedBox(height: 30),
                                
                                // 电台信息
                                _buildStationInfo(context, currentStation),
                                
                                const SizedBox(height: 12),
                                
                                // 播放列表信息
                                _buildPlaylistInfo(context, ref),
                                
                                const SizedBox(height: 24),
                                
                                // 播放状态
                                playbackAsync.when(
                                  data: (playback) => _buildPlaybackStatus(context, playback),
                                  loading: () => _buildLoadingStatus(context),
                                  error: (error, stack) => _buildErrorStatus(context, error),
                                ),
                                
                                const SizedBox(height: 30),
                                
                                // 播放控制
                                _buildPlayControls(context, ref, currentStation, playbackAsync, favoritesAsync),
                                
                                const SizedBox(height: 40),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建现代化动态背景
  Widget _buildBackground(BuildContext context, StationSimple station) {
    return Stack(
      children: [
        // 主渐变背景
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0F0F23), // 深蓝黑
                const Color(0xFF1A1A2E), // 深紫蓝
                const Color(0xFF16213E), // 深蓝
                const Color(0xFF0F3460), // 中蓝
              ],
              stops: const [0.0, 0.3, 0.7, 1.0],
            ),
          ),
        ),

        // 动态粒子背景
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: _ParticleBackgroundPainter(
                  animationValue: _fadeAnimation.value,
                  particleCount: 30,
                  particleColor: const Color(0xFF00FFFF).withOpacity(0.3),
                ),
              );
            },
          ),
        ),

        // 霓虹发光效果层
        Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.5,
              colors: [
                const Color(0xFF00FFFF).withOpacity(0.1),
                const Color(0xFFFF00FF).withOpacity(0.05),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建顶部导航
  Widget _buildTopNavigation(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.keyboard_arrow_down, size: 28),
              color: Colors.grey.shade700,
              onPressed: () {
                print('🎵 PlayerModal close button tapped');
                widget.onClose();
              },
            ),
          ),
          // 可以在这里添加更多按钮，比如分享、更多选项等
          const SizedBox(width: 48), // 占位保持对称
        ],
      ),
    );
  }

  /// 构建轮播图式电台展示
  /// 
  /// 功能实现: 显示当前电台和前后电台的图片，支持滑动切换
  /// 实现方案: 使用PageView实现轮播图效果，显示3张图片（上一首、当前、下一首）
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
    Widget _buildStationCarousel(BuildContext context, WidgetRef ref, StationSimple currentStation) {
    final playlistContext = ref.watch(currentPlaylistProvider);
    
    // 如果没有播放列表，显示单张图片
    if (playlistContext == null) {
      return _buildStationCover(context, currentStation);
    }
    
    // 构建实际存在的电台列表（不包含null占位符）
    final List<StationSimple> actualStations = [];
    final List<int> stationMapping = []; // 映射到原始索引
    
    if (playlistContext.previousStation != null) {
      actualStations.add(playlistContext.previousStation!);
      stationMapping.add(-1); // 上一首
    }
    
    actualStations.add(currentStation);
    stationMapping.add(0); // 当前
    
    if (playlistContext.nextStation != null) {
      actualStations.add(playlistContext.nextStation!);
      stationMapping.add(1); // 下一首
    }
    
    // 如果只有一个电台，直接显示单张图片
    if (actualStations.length == 1) {
      return _buildStationCover(context, currentStation);
    }
    
    // 计算当前电台在实际列表中的索引
    final currentStationIndex = actualStations.indexWhere((s) => s.id == currentStation.id);
    
    // 确保PageController在正确位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _pageController.hasClients) {
        if (_currentCarouselIndex != currentStationIndex) {
          _currentCarouselIndex = currentStationIndex;
          _pageController.animateToPage(
            currentStationIndex,
            duration: const Duration(milliseconds: 50),
            curve: Curves.easeOut,
          );
        }
      }
    });
    
    return SizedBox(
      height: MediaQuery.of(context).size.width * 0.75,
      child: PageView.builder(
        controller: _pageController,
        itemCount: actualStations.length,
        physics: const BouncingScrollPhysics(), // 使用iOS风格的弹性滑动物理效果
        pageSnapping: true, // 启用页面捕捉
        allowImplicitScrolling: false, // 禁用隐式滚动
        onPageChanged: (index) {
          print('🎵 轮播图页面切换到: $index, 映射: ${stationMapping[index]}');
          
          // 防止重复触发和正在切换时的干扰
          if (_currentCarouselIndex == index || _isChangingStation) return;
          
          _currentCarouselIndex = index;
          
          // 根据映射关系确定操作
          final mappedIndex = stationMapping[index];
          
          if (mappedIndex == -1) {
            // 滑动到上一首
            _isChangingStation = true;
            _playPrevious();
            // 立即回弹，提供丝滑体验
            Future.delayed(const Duration(milliseconds: 100), () {
              _isChangingStation = false;
              _resetToCurrentCenter(actualStations, currentStation);
            });
          } else if (mappedIndex == 1) {
            // 滑动到下一首
            _isChangingStation = true;
            _playNext();
            // 立即回弹，提供丝滑体验
            Future.delayed(const Duration(milliseconds: 100), () {
              _isChangingStation = false;
              _resetToCurrentCenter(actualStations, currentStation);
            });
          }
        },
        itemBuilder: (context, index) {
          final station = actualStations[index];
          final isCurrentStation = station.id == currentStation.id;
          
          return _buildCarouselStationCover(
            context, 
            station, 
            isCurrentStation,
            opacity: isCurrentStation ? 1.0 : 0.6,
          );
        },
      ),
    );
  }

  /// 构建轮播图中的电台封面
  Widget _buildCarouselStationCover(
    BuildContext context, 
    StationSimple station, 
    bool isCurrentStation,
    {double opacity = 1.0}
  ) {
    final size = MediaQuery.of(context).size.width * 0.75;
    final actualSize = isCurrentStation ? size : size * 0.85; // 非当前电台稍小一些
    
    return Center(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200), // 缩短动画时长
        curve: Curves.easeOutCubic, // 使用更平滑的动画曲线
        width: actualSize,
        height: actualSize,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: opacity,
          child: Transform.scale(
            scale: isCurrentStation ? 1.0 : 0.95, // 添加缩放效果
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: isCurrentStation ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.25),
                    blurRadius: 25,
                    offset: const Offset(0, 12),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.purple.withOpacity(0.15),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 3,
                  ),
                ] : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Stack(
                  children: [
                    // 电台图片
                    StationImageBuilder.buildStationImage(
                      station: station,
                      width: actualSize,
                      height: actualSize,
                      fit: BoxFit.cover,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    
                    // 渐变覆盖层
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.03),
                            Colors.black.withOpacity(0.08),
                          ],
                        ),
                      ),
                    ),
                    
                    // 非当前电台显示淡化指示
                    if (!isCurrentStation)
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.black.withOpacity(0.15),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }



  /// 构建电台封面（使用统一的图片构建工具）
  Widget _buildStationCover(BuildContext context, StationSimple station) {
    final size = MediaQuery.of(context).size.width * 0.75;
    
    return Center(
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.purple.withOpacity(0.2),
              blurRadius: 50,
              offset: const Offset(0, 25),
              spreadRadius: 5,
            ),
          ],
        ),
        child: Stack(
          children: [
            // 电台图片（使用统一的图片构建工具）
            StationImageBuilder.buildStationImage(
              station: station,
              width: size,
              height: size,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(20),
            ),

            // 渐变覆盖层
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.05),
                    Colors.black.withOpacity(0.1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建播放列表信息
  /// 
  /// 功能实现: 显示当前播放列表的状态信息
  /// 实现方案: 监听播放列表上下文Provider，显示列表标题和位置
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Widget _buildPlaylistInfo(BuildContext context, WidgetRef ref) {
    final playlistContext = ref.watch(currentPlaylistProvider);
    
    if (playlistContext == null) {
      return const SizedBox.shrink();
    }
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.queue_music,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  playlistContext.sourceTitle,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${playlistContext.currentIndex + 1}/${playlistContext.stations.length}',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // 滑动提示
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.keyboard_arrow_left,
              size: 16,
              color: Colors.grey.shade500,
            ),
            const SizedBox(width: 4),
            Text(
              'Swipe to change stations',
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 11,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_right,
              size: 16,
              color: Colors.grey.shade500,
            ),
          ],
        ),
      ],
    );
  }

  /// 构建电台信息
  Widget _buildStationInfo(BuildContext context, StationSimple station) {
    return Column(
      children: [
        // 电台名称
        TextOverflowHandler.safeText(
          station.name,
          style: TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
            height: 1.2,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
        ),
        
        const SizedBox(height: 6),
        
        // 国家信息
        if (station.country.isNotEmpty)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.public,
                size: 14,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              TextOverflowHandler.safeText(
                station.country,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
              ),
            ],
          ),
      ],
    );
  }

  /// 构建播放状态
  Widget _buildPlaybackStatus(BuildContext context, CurrentPlayback playback) {
    String statusText;
    Color statusColor;
    IconData statusIcon;
    bool showVisualizer = false;

    switch (playback.state) {
      case PlaybackState.loading:
        statusText = 'connecting'.tr();
        statusColor = Colors.orange;
        statusIcon = Icons.sync;
        break;
      case PlaybackState.playing:
        statusText = 'now_playing'.tr();
        statusColor = Colors.green;
        statusIcon = Icons.play_circle_filled;
        showVisualizer = true;
        break;
      case PlaybackState.paused:
        statusText = 'pause'.tr();
        statusColor = Colors.grey;
        statusIcon = Icons.pause_circle_filled;
        break;
      case PlaybackState.error:
        statusText = 'connection_error'.tr();
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case PlaybackState.stopped:
        statusText = 'stopped'.tr();
        statusColor = Colors.grey;
        statusIcon = Icons.stop_circle;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 18,
          ),
          const SizedBox(width: 8),
          TextOverflowHandler.safeText(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
          ),
          if (showVisualizer) ...[
            const SizedBox(width: 10),
            AudioVisualizer(
              isPlaying: showVisualizer,
              barCount: 4,
              barWidth: 2.0,
              barSpacing: 1.5,
              maxHeight: 14.0,
              minHeight: 3.0,
              color: statusColor,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingStatus(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 14,
            height: 14,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
          ),
          const SizedBox(width: 8),
          TextOverflowHandler.safeText(
            'connecting'.tr(),
            style: const TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorStatus(BuildContext context, Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error,
            color: Colors.red,
            size: 18,
          ),
          const SizedBox(width: 8),
          TextOverflowHandler.safeText(
            'connection_error'.tr(),
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  /// 构建播放控制
  Widget _buildPlayControls(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<CurrentPlayback> playbackAsync,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 收藏按钮
          _buildFavoriteButton(context, ref, station, favoritesAsync),
          // 播放/暂停按钮
          _buildPlayButton(context, ref, station, playbackAsync),
          // 占位符保持布局平衡
          const SizedBox(width: 48, height: 48),
        ],
      ),
    );
  }

  /// 构建收藏按钮
  Widget _buildFavoriteButton(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    return favoritesAsync.when(
      data: (favorites) {
        final isFavorite = favorites.any((fav) => fav.id == station.id);
        return Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: () async {
                try {
                  final storageService = ref.read(storageServiceProvider);
                  if (isFavorite) {
                    await storageService.removeFromFavorites(station.id);
                  } else {
                    await storageService.addToFavorites(station);
                  }
                  ref.invalidate(favoriteStationsProvider);
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: TextOverflowHandler.safeText(
                          '操作失败: $e',
                          maxLines: 1,
                        ),
                        backgroundColor: Colors.red.shade600,
                      ),
                    );
                  }
                }
              },
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    key: ValueKey(isFavorite),
                    color: isFavorite ? Colors.red.shade400 : Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ),
      error: (error, stack) => Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.favorite_border,
          color: Colors.white.withOpacity(0.5),
          size: 20,
        ),
      ),
    );
  }

  /// 构建播放按钮
  Widget _buildPlayButton(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<CurrentPlayback> playbackAsync,
  ) {
    return playbackAsync.when(
      data: (playback) {
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF00FFFF).withOpacity(0.3),
                const Color(0xFF00FFFF).withOpacity(0.1),
              ],
            ),
            shape: BoxShape.circle,
            border: Border.all(
              color: const Color(0xFF00FFFF).withOpacity(0.8),
              width: 2,
            ),
            boxShadow: [
              // 内发光
              BoxShadow(
                color: const Color(0xFF00FFFF).withOpacity(0.4),
                blurRadius: 8,
                spreadRadius: -2,
              ),
              // 外发光
              BoxShadow(
                color: const Color(0xFF00FFFF).withOpacity(0.6),
                blurRadius: 20,
                spreadRadius: 4,
              ),
              // 远距离发光
              BoxShadow(
                color: const Color(0xFF00FFFF).withOpacity(0.3),
                blurRadius: 40,
                spreadRadius: 8,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(32),
              onTap: () {
                final audioService = ref.read(audioServiceProvider);
                switch (playback.state) {
                  case PlaybackState.playing:
                    audioService.pause();
                    break;
                  case PlaybackState.paused:
                    // 暂停状态：快速恢复播放，无需重新加载资源
                    print('📻 PlayerModal恢复播放 - 使用缓存资源');
                    audioService.resume();
                    break;
                  case PlaybackState.stopped:
                    // 停止状态：重新开始播放，需要重新加载资源
                    print('📻 PlayerModal重新播放 - 加载新资源');
                    audioService.playStation(station);
                    break;
                  case PlaybackState.loading:
                    break;
                  case PlaybackState.error:
                    audioService.playStation(station);
                    break;
                }
              },
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: _getPlayIcon(playback.state),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.3),
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ),
      error: (error, stack) => Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.8),
          shape: BoxShape.circle,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(32),
            onTap: () {
              ref.read(audioServiceProvider).playStation(station);
            },
            child: const Center(
              child: Icon(
                Icons.refresh,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取播放图标
  Widget _getPlayIcon(PlaybackState state) {
    switch (state) {
      case PlaybackState.playing:
        return const Icon(
          Icons.pause,
          color: Colors.black87,
          size: 28,
          key: ValueKey('pause'),
        );
      case PlaybackState.paused:
      case PlaybackState.stopped:
        return const Icon(
          Icons.play_arrow,
          color: Colors.black87,
          size: 28,
          key: ValueKey('play'),
        );
      case PlaybackState.loading:
        return const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.black87),
          ),
        );
      case PlaybackState.error:
        return const Icon(
          Icons.refresh,
          color: Colors.black87,
          size: 28,
          key: ValueKey('error'),
        );
    }
  }
}

/// 粒子背景绘制器
class _ParticleBackgroundPainter extends CustomPainter {
  final double animationValue;
  final int particleCount;
  final Color particleColor;

  _ParticleBackgroundPainter({
    required this.animationValue,
    required this.particleCount,
    required this.particleColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = particleColor
      ..style = PaintingStyle.fill;

    // 生成粒子位置
    for (int i = 0; i < particleCount; i++) {
      final x = (i * 37.0 + animationValue * 100) % size.width;
      final y = (i * 23.0 + animationValue * 50) % size.height;
      final radius = 1.0 + (i % 3) * 0.5;

      // 添加闪烁效果
      final opacity = (0.3 + 0.7 * ((animationValue + i * 0.1) % 1.0));
      paint.color = particleColor.withOpacity(opacity);

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}