import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import 'dart:ui' show ImageFilter;
import '../router/app_router.dart';
import '../utils/text_overflow_checker.dart';
import 'mini_player.dart';
import 'player_modal.dart';
import 'app_top_bar.dart';
import '../../features/home/<USER>';
import '../../features/explore/explore_page.dart';
import '../../features/library/library_page.dart';

/// 应用程序主要脚手架
class MainScaffold extends ConsumerStatefulWidget {
  const MainScaffold({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  ConsumerState<MainScaffold> createState() => _MainScaffoldState();
}

class _MainScaffoldState extends ConsumerState<MainScaffold> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const HomePage(),
    const ExplorePage(),
    const LibraryPage(),
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateCurrentIndex();
  }

  void _updateCurrentIndex() {
    final location = GoRouterState.of(context).location;
    for (int i = 0; i < BottomNavItem.items.length; i++) {
      if (BottomNavItem.items[i].route == location) {
        if (_currentIndex != i) {
          setState(() {
            _currentIndex = i;
          });
        }
        break;
      }
    }
  }

  void _onItemTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
      final item = BottomNavItem.items[index];
      context.go(item.route);
    }
  }

  void _onMiniPlayerTapped() {
    print('🎵 MiniPlayer tapped - expanding player modal');
    _showPlayerModal();
  }
  
  void _showPlayerModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9, // 初始高度为屏幕90%
        minChildSize: 0.5,     // 最小高度为屏幕50%
        maxChildSize: 0.95,    // 最大高度为屏幕95%
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: PlayerModal(
            onClose: () => Navigator.of(context).pop(),
            animation: kAlwaysCompleteAnimation,
          ),
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final location = GoRouterState.of(context).location;
    final isMainPage = [
      AppRouter.home,
      AppRouter.explore,
      AppRouter.library,
    ].contains(location);

    // 如果不是主要页面，直接返回child
    if (!isMainPage) {
      return widget.child;
    }

    return TextOverflowChecker.checkAndWrap(
      Container(
        // 统一的背景渐变色，覆盖整个屏幕
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: const [
              Color(0xFF0F0F23), // 深蓝黑
              Color(0xFF1A1A2E), // 深紫蓝
              Color(0xFF16213E), // 深蓝
              Color(0xFF0F3460), // 中蓝
            ],
            stops: const [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent, // 透明背景，使用Container的渐变
          appBar: const AppTopBar(),
          body: Stack(
            children: [
              // 主要内容区域
              Column(
                children: [
                  Expanded(
                    child: TextOverflowChecker.autoFixTree(
                      IndexedStack(
                        index: _currentIndex,
                        children: _pages,
                      ),
                    ),
                  ),
                  // MiniPlayer - 始终显示
                  MiniPlayer(
                    onTap: _onMiniPlayerTapped,
                  ),
                ],
              ),


            ],
          ),
          bottomNavigationBar: _buildModernBottomNavBar(),
        ),
      ),
      debugLabel: 'MainScaffold',
    );
  }

  /// 构建现代化浮动底部导航栏 - 统一背景色系和增强玻璃态效果
  Widget _buildModernBottomNavBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 24),
      height: 70,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(35),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFF0F0F23).withOpacity(0.85), // 深蓝黑
                  const Color(0xFF1A1A2E).withOpacity(0.80), // 深紫蓝
                  const Color(0xFF16213E).withOpacity(0.75), // 深蓝
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
              borderRadius: BorderRadius.circular(35),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                // 主阴影
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 12),
                  spreadRadius: 0,
                ),
                // 高光效果
                BoxShadow(
                  color: Colors.white.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, -6),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: BottomNavItem.items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isSelected = index == _currentIndex;

                return _buildNavItem(
                  icon: item.icon,
                  label: item.label.tr(),
                  isSelected: isSelected,
                  onTap: () => _onItemTapped(index),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建单个导航项 - 简洁设计，去掉霓虹效果
  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: isSelected ? BoxDecoration(
          // 简洁的选中状态背景
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.15),
              Colors.white.withOpacity(0.08),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ) : null,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Colors.white // 选中时使用清晰的白色
                  : Colors.white.withOpacity(0.6),
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? Colors.white // 选中时使用清晰的白色
                    : Colors.white.withOpacity(0.6),
                letterSpacing: 0.3,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
