import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/models.dart';
import 'audio_service.dart';
import 'media_control_initializer.dart';

/// 媒体控制测试辅助类
/// 
/// 提供测试锁屏播放器功能的辅助方法
/// 功能实现: 模拟播放场景，测试媒体控制功能
/// 实现方案: 提供预设的测试数据和场景
/// 影响范围: 开发和测试阶段
/// 实现日期: 2025-01-27
class MediaControlTestHelper {
  
  /// 创建测试播放列表
  static PlaylistContext createTestPlaylist() {
    final testStations = [
      const StationSimple(
        id: 'test_station_1',
        name: 'Test Radio 1',
        url: 'https://example.com/stream1',
        country: 'Test Country',
        language: 'English',
        favicon: 'https://example.com/icon1.png',
      ),
      const StationSimple(
        id: 'test_station_2',
        name: 'Test Radio 2',
        url: 'https://example.com/stream2',
        country: 'Test Country',
        language: 'English',
        favicon: 'https://example.com/icon2.png',
      ),
      const StationSimple(
        id: 'test_station_3',
        name: 'Test Radio 3',
        url: 'https://example.com/stream3',
        country: 'Test Country',
        language: 'English',
        favicon: 'https://example.com/icon3.png',
      ),
    ];

    return PlaylistContext(
      sourceType: PlaylistSourceType.hotRecommendations,
      stations: testStations,
      currentIndex: 1, // 从中间开始，测试上一首和下一首
      sourceTitle: 'Test Playlist',
      sourceMetadata: {'test': true},
    );
  }

  /// 测试基础播放控制
  static Future<void> testBasicPlaybackControl(WidgetRef ref) async {
    print('🧪 MediaControlTestHelper: 开始测试基础播放控制');
    
    try {
      final audioService = ref.read(audioServiceProvider);
      final testStation = const StationSimple(
        id: 'test_basic_station',
        name: 'Basic Test Radio',
        url: 'https://example.com/basic_stream',
        country: 'Test',
        language: 'English',
      );

      // 测试播放
      print('🧪 测试播放功能...');
      await audioService.playStation(testStation);
      
      // 等待一段时间
      await Future.delayed(const Duration(seconds: 2));
      
      // 测试暂停
      print('🧪 测试暂停功能...');
      await audioService.pause();
      
      // 等待一段时间
      await Future.delayed(const Duration(seconds: 1));
      
      // 测试恢复
      print('🧪 测试恢复功能...');
      await audioService.resume();
      
      print('✅ MediaControlTestHelper: 基础播放控制测试完成');
      
    } catch (e) {
      print('❌ MediaControlTestHelper: 基础播放控制测试失败: $e');
    }
  }

  /// 测试播放列表控制
  static Future<void> testPlaylistControl(WidgetRef ref) async {
    print('🧪 MediaControlTestHelper: 开始测试播放列表控制');
    
    try {
      final audioService = ref.read(audioServiceProvider);
      final testPlaylist = createTestPlaylist();
      
      // 开始播放列表中的第二首（索引1）
      print('🧪 开始播放测试列表...');
      await audioService.playStationWithPlaylist(
        station: testPlaylist.currentStation,
        playlistContext: testPlaylist,
      );
      
      // 等待播放开始
      await Future.delayed(const Duration(seconds: 2));
      
      // 测试上一首
      print('🧪 测试上一首功能...');
      final prevSuccess = await audioService.playPrevious();
      print('🧪 上一首结果: $prevSuccess');
      
      // 等待切换完成
      await Future.delayed(const Duration(seconds: 2));
      
      // 测试下一首
      print('🧪 测试下一首功能...');
      final nextSuccess = await audioService.playNext();
      print('🧪 下一首结果: $nextSuccess');
      
      // 再次测试下一首
      await Future.delayed(const Duration(seconds: 2));
      final nextSuccess2 = await audioService.playNext();
      print('🧪 第二次下一首结果: $nextSuccess2');
      
      // 测试边界情况 - 尝试超出列表范围
      await Future.delayed(const Duration(seconds: 2));
      final nextSuccess3 = await audioService.playNext();
      print('🧪 边界测试下一首结果: $nextSuccess3 (应该为false)');
      
      print('✅ MediaControlTestHelper: 播放列表控制测试完成');
      
    } catch (e) {
      print('❌ MediaControlTestHelper: 播放列表控制测试失败: $e');
    }
  }

  /// 测试媒体控制初始化
  static Future<void> testMediaControlInitialization(WidgetRef ref) async {
    print('🧪 MediaControlTestHelper: 开始测试媒体控制初始化');
    
    try {
      // 检查初始化状态
      final isInitialized = MediaControlInitializer.isInitialized;
      print('🧪 媒体控制初始化状态: $isInitialized');
      
      // 获取音频处理器
      final audioHandler = MediaControlInitializer.audioHandler;
      print('🧪 音频处理器状态: ${audioHandler != null ? '已创建' : '未创建'}');
      
      if (audioHandler != null) {
        print('✅ MediaControlTestHelper: 媒体控制初始化测试通过');
      } else {
        print('❌ MediaControlTestHelper: 媒体控制初始化测试失败 - 音频处理器未创建');
      }
      
    } catch (e) {
      print('❌ MediaControlTestHelper: 媒体控制初始化测试失败: $e');
    }
  }

  /// 运行完整测试套件
  static Future<void> runFullTestSuite(WidgetRef ref) async {
    print('🧪 MediaControlTestHelper: 开始运行完整测试套件');
    
    // 测试1: 媒体控制初始化
    await testMediaControlInitialization(ref);
    await Future.delayed(const Duration(seconds: 1));
    
    // 测试2: 基础播放控制
    await testBasicPlaybackControl(ref);
    await Future.delayed(const Duration(seconds: 2));
    
    // 测试3: 播放列表控制
    await testPlaylistControl(ref);
    
    print('✅ MediaControlTestHelper: 完整测试套件运行完成');
  }

  /// 获取当前播放状态信息
  static Future<void> printCurrentStatus(WidgetRef ref) async {
    try {
      final currentStation = ref.read(currentStationProvider);
      final playlistContext = ref.read(currentPlaylistProvider);
      final audioService = ref.read(audioServiceProvider);
      
      print('📊 MediaControlTestHelper: 当前状态信息');
      print('   当前电台: ${currentStation?.name ?? '无'}');
      print('   播放列表: ${playlistContext?.sourceTitle ?? '无'}');
      print('   列表位置: ${playlistContext != null ? '${playlistContext.currentIndex + 1}/${playlistContext.stations.length}' : '无'}');
      print('   可播放上一首: ${audioService.canPlayPrevious}');
      print('   可播放下一首: ${audioService.canPlayNext}');
      print('   播放状态: ${audioService.currentState}');
      
    } catch (e) {
      print('❌ MediaControlTestHelper: 获取状态信息失败: $e');
    }
  }
}

/// 媒体控制测试辅助提供者
final mediaControlTestHelperProvider = Provider<MediaControlTestHelper>((ref) {
  return MediaControlTestHelper();
});
