import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../utils/xor_crypto.dart';
import '../config/app_config.dart';

/// API服务基础类
/// 提供统一的加密解密、错误处理、日志记录等功能
abstract class BaseApiService {
  late final Dio _dio;
  
  /// 构造函数
  BaseApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: Duration(seconds: AppConfig.apiConnectTimeout),
      receiveTimeout: Duration(seconds: AppConfig.apiReceiveTimeout),
      sendTimeout: Duration(seconds: AppConfig.apiSendTimeout),
      headers: {
        'Content-Type': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }

  /// 设置拦截器
  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (AppConfig.enableApiLogging) {
          print('🚀 API Request: ${options.method} ${options.path}');
          print('📤 Request Data: ${options.data}');
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        if (AppConfig.enableApiLogging) {
          print('✅ API Response: ${response.statusCode} ${response.requestOptions.path}');
          // print('📥 Response Data: ${response.data}');
        }
        handler.next(response);
      },
      onError: (error, handler) {
        if (AppConfig.enableApiLogging) {
          print('❌ API Error: ${error.message}');
          print('🔍 Error Details: ${error.response?.data}');
        }
        handler.next(error);
      },
    ));
  }

  /// 执行加密的POST请求
  /// 
  /// [path] 请求路径
  /// [params] 请求参数（将被加密）
  /// [headers] 额外的请求头
  /// 
  /// 返回解密后的响应数据
  Future<Map<String, dynamic>> postEncrypted(
    String path,
    Map<String, dynamic> params, {
    Map<String, String>? headers,
  }) async {
    try {
      // 创建加密的请求体
      final encryptedRequest = XorCrypto.createEncryptedRequest(params);
      
      // 发送请求
      final response = await _dio.post(
        path,
        data: encryptedRequest,
        options: Options(headers: headers),
      );
      
      // 检查HTTP状态码
      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'HTTP错误: ${response.statusCode}',
        );
      }
      
      // 解析并解密响应
      final responseData = response.data as Map<String, dynamic>;
      return XorCrypto.parseEncryptedResponse(responseData);
      
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ApiException('请求失败: $e');
    }
  }

  /// 执行普通的GET请求（不加密）
  /// 
  /// [path] 请求路径
  /// [queryParameters] 查询参数
  /// [headers] 额外的请求头
  /// 
  /// 返回响应数据
  Future<Map<String, dynamic>> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      
      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'HTTP错误: ${response.statusCode}',
        );
      }
      
      return response.data as Map<String, dynamic>;
      
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ApiException('请求失败: $e');
    }
  }

  /// 处理Dio异常
  ApiException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return ApiException('连接超时，请检查网络连接');
      case DioExceptionType.sendTimeout:
        return ApiException('发送超时，请重试');
      case DioExceptionType.receiveTimeout:
        return ApiException('接收超时，请重试');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        switch (statusCode) {
          case 400:
            return ApiException('请求参数错误');
          case 401:
            return ApiException('未授权，请重新登录');
          case 403:
            return ApiException('禁止访问');
          case 404:
            return ApiException('接口不存在');
          case 500:
            return ApiException('服务器内部错误');
          default:
            return ApiException('网络错误 ($statusCode)');
        }
      case DioExceptionType.cancel:
        return ApiException('请求已取消');
      case DioExceptionType.connectionError:
        return ApiException('网络连接失败，请检查网络设置');
      default:
        return ApiException('未知错误: ${e.message}');
    }
  }

  /// 释放资源
  void dispose() {
    _dio.close();
  }
}

/// API异常类
class ApiException implements Exception {
  final String message;
  final int? code;
  
  const ApiException(this.message, [this.code]);
  
  @override
  String toString() => 'ApiException: $message${code != null ? ' (code: $code)' : ''}';
}
