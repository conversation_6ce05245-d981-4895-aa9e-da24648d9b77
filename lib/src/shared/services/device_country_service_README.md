# 设备国家检测服务

## 概述

`DeviceCountryService` 是一个用于检测用户设备当前国家/地区设置的服务。该服务通过多种方式尝试获取设备的国家代码，并提供相应的国家名称映射功能。

## 功能特性

### 🔍 多重检测机制

1. **系统语言环境检测**
   - 从 `PlatformDispatcher.instance.locale` 获取系统默认语言环境
   - 支持多语言环境检测
   - 优先级：最高

2. **平台特定检测**
   - **Android**: 通过原生插件获取SIM卡、网络运营商、系统语言环境、时区信息
   - **iOS**: 通过原生插件获取运营商信息、系统语言环境、时区信息
   - 优先级：中等

3. **备用检测机制**
   - 环境变量检测（Android）
   - 时区映射检测
   - 优先级：最低

### 🌍 国家代码映射

服务内置了常见国家代码到英文名称的映射表，支持：
- ISO 3166-1 alpha-2 格式的国家代码（如：CN, US, JP）
- 对应的英文国家名称（如：China, United States, Japan）
- 覆盖全球主要国家和地区

### ✅ 数据验证

- 国家代码格式验证（2位字母）
- 空值和无效值处理
- 异常情况的优雅降级

## 使用方法

### 基本用法

```dart
import '../services/device_country_service.dart';

// 获取设备国家代码
final countryCode = await DeviceCountryService.getDeviceCountryCode();
if (countryCode != null) {
  print('设备国家代码: $countryCode'); // 输出: CN
}

// 根据国家代码获取英文名称
final countryName = DeviceCountryService.getCountryNameByCode('CN');
print('国家名称: $countryName'); // 输出: China

// 验证国家代码格式
final isValid = DeviceCountryService.isValidCountryCode('CN');
print('格式有效: $isValid'); // 输出: true
```

### 在Provider中使用

```dart
// 在 country_provider.dart 中的使用示例
Future<Country> _detectDeviceCountry() async {
  try {
    // 获取设备国家代码
    final deviceCountryCode = await DeviceCountryService.getDeviceCountryCode();
    
    if (deviceCountryCode != null) {
      // 根据国家代码获取英文名称
      final countryName = DeviceCountryService.getCountryNameByCode(deviceCountryCode);
      
      if (countryName != null && state.countries.isNotEmpty) {
        // 在国家列表中查找匹配的国家
        final deviceCountry = state.countries.where((c) => 
          c.name == countryName || c.code.toUpperCase() == deviceCountryCode.toUpperCase()
        ).firstOrNull;
        
        if (deviceCountry != null) {
          return deviceCountry;
        }
      }
    }
  } catch (e) {
    print('检测设备国家时出错: $e');
  }
  
  // 检测失败，返回备用国家
  return _getFallbackCountry();
}
```

## 原生插件集成

### Android 集成

1. **插件文件**: `android/app/src/main/kotlin/com/example/verygoodcore/DeviceCountryPlugin.kt`
2. **检测方式**:
   - SIM卡国家代码 (`TelephonyManager.getSimCountryIso()`)
   - 网络运营商国家代码 (`TelephonyManager.getNetworkCountryIso()`)
   - 系统语言环境 (`Locale.getDefault().getCountry()`)
   - 时区映射

3. **权限要求**: 无需特殊权限

### iOS 集成

1. **插件文件**: `ios/Runner/DeviceCountryPlugin.swift`
2. **检测方式**:
   - 运营商信息 (`CTTelephonyNetworkInfo`)
   - 系统语言环境 (`Locale.current.regionCode`)
   - 时区映射

3. **权限要求**: 无需特殊权限

## 错误处理

### 异常情况处理

- **网络不可用**: 不影响检测，使用本地方法
- **权限不足**: 自动降级到其他检测方式
- **原生插件调用失败**: 使用Flutter层面的检测方法
- **数据格式错误**: 自动验证和过滤无效数据

### 日志记录

服务使用统一的日志系统记录关键信息：

```dart
_logger.info('从系统语言环境获取到国家代码: $countryCode');
_logger.warning('未找到国家代码对应的英文名称');
_logger.error('获取设备国家代码时出错: $e');
```

## 性能优化

### 缓存机制

- 设备国家检测结果会被缓存在 `SharedPreferences` 中
- 使用 `_deviceCountryDetectedKey` 标记避免重复检测
- 首次检测后，后续启动直接使用缓存结果

### 异步处理

- 所有检测操作都是异步的，不会阻塞UI线程
- 支持超时处理和错误恢复
- 优雅的降级机制确保应用正常运行

## 测试建议

### 单元测试

```dart
test('设备国家代码检测', () async {
  final countryCode = await DeviceCountryService.getDeviceCountryCode();
  expect(countryCode, isNotNull);
  expect(DeviceCountryService.isValidCountryCode(countryCode), isTrue);
});

test('国家代码映射', () {
  final countryName = DeviceCountryService.getCountryNameByCode('CN');
  expect(countryName, equals('China'));
});
```

### 集成测试

- 在不同设备上测试检测准确性
- 测试网络异常情况下的降级处理
- 验证多语言环境下的检测结果

## 注意事项

1. **隐私考虑**: 该服务仅获取国家级别的地理信息，不涉及精确位置
2. **准确性**: 检测结果可能因设备设置、网络环境等因素有所差异
3. **兼容性**: 支持 Android 5.0+ 和 iOS 12.0+
4. **性能**: 首次检测可能需要几百毫秒，后续使用缓存结果

## 更新日志

- **v1.0.0**: 初始版本，支持基本的设备国家检测功能
- 支持Android和iOS平台
- 内置常见国家代码映射表
- 完整的错误处理和日志记录机制
