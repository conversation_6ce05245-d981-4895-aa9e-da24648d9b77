import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_service/audio_service.dart';
import '../models/models.dart';
import 'storage_service.dart';

/// 音频播放服务
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _player = AudioPlayer();
  final StreamController<CurrentPlayback> _playbackController =
      StreamController<CurrentPlayback>.broadcast();

  StationSimple? _currentStation;
  PlaybackState _currentState = PlaybackState.stopped;
  StorageService? _storageService;
  dynamic _ref;
  
  // 播放列表管理
  PlaylistContext? _currentPlaylist;
  
  // 自动重试机制
  int _retryCount = 0;
  static const int _maxRetries = 3;
  Timer? _retryTimer;
  
  // HLS流特殊处理
  Timer? _stallRecoveryTimer;
  int _stallCount = 0;
  static const int _maxStallRetries = 5;
  DateTime? _lastStallTime;
  
  // 状态管理保护
  bool _isProcessingStateChange = false;
  bool _isReconnecting = false;
  
  // BUG修复: 用户手动暂停标志，避免误判为意外暂停
  // 修复策略: 添加标志区分用户主动暂停和意外中断
  // 影响范围: audio_service.dart:73-85行
  // 修复日期: 2025-01-27
  bool _isManualPause = false;

  // 媒体控制服务集成
  bool _mediaControlsInitialized = false;

  /// 当前播放状态流
  Stream<CurrentPlayback> get playbackStream => _playbackController.stream;

  /// 当前播放的电台
  StationSimple? get currentStation => _currentStation;

  /// 当前播放状态
  PlaybackState get currentState => _currentState;

  /// 当前播放列表上下文
  PlaylistContext? get currentPlaylist => _currentPlaylist;

  /// 是否有播放列表
  bool get hasPlaylist => _currentPlaylist != null;

  /// 是否可以播放上一首
  bool get canPlayPrevious => _currentPlaylist?.canPlayPrevious ?? false;

  /// 是否可以播放下一首
  bool get canPlayNext => _currentPlaylist?.canPlayNext ?? false;

  /// 初始化音频服务
  Future<void> initialize([StorageService? storageService, dynamic ref]) async {
    print('🎵 AudioService: 初始化音频服务');
    _storageService = storageService;
    _ref = ref;

    // 初始化媒体控制
    await _initializeMediaControls();

    // 监听播放器状态变化
    _player.playerStateStream.listen((playerState) {
      print('🎵 AudioService: 播放器状态变化 - ${playerState.processingState}, playing: ${playerState.playing}');
      
              // 检测意外的暂停（排除用户手动暂停）
        if (_currentState == PlaybackState.playing && 
            playerState.processingState == ProcessingState.ready && 
            !playerState.playing &&
            !_isManualPause) { // BUG修复: 排除用户手动暂停
          print('⚠️ AudioService: 检测到意外暂停！');
          print('🔍 当前电台: ${_currentStation?.name ?? 'unknown'}');
          print('🔍 当前URL: ${_currentStation?.url ?? 'unknown'}');
          print('🔍 播放器状态: ${playerState.processingState}');
          print('🔍 播放标志: ${playerState.playing}');
          
          // 尝试自动恢复播放
          _attemptAutoResume();
        } else if (_isManualPause && !playerState.playing) {
          // BUG修复: 用户手动暂停，不触发自动恢复
          print('✅ AudioService: 用户手动暂停，跳过自动恢复');
        }
      
      _updatePlaybackState(playerState);
    });

    // 监听播放位置变化（减少日志频率）
    _player.positionStream.listen((position) {
      _emitCurrentPlayback(position: position);
    });

    // 监听播放错误（使用当前版本兼容的方式）

    // 监听播放事件流（仅用于状态跟踪，不处理错误）
    _player.playbackEventStream.listen(
      (event) {
        // 记录重要的播放事件
        if (event.processingState == ProcessingState.loading) {
          print('🎵 AudioService: 开始加载音频流...');
        } else if (event.processingState == ProcessingState.ready) {
          print('🎵 AudioService: 音频流准备就绪 - Duration: ${event.duration}');
        } else if (event.processingState == ProcessingState.buffering) {
          print('🎵 AudioService: 音频缓冲中...');
        } else if (event.processingState == ProcessingState.completed) {
          print('⚠️ AudioService: 音频播放完成 - 这可能导致意外停止');
        }
        
        // 检测播放位置异常（仅记录，不立即处理）
        if (event.duration != null && event.updatePosition > event.duration!) {
          print('⚠️ AudioService: 播放位置异常 - Position: ${event.updatePosition}, Duration: ${event.duration}');
          
          // 延迟处理位置异常，避免状态冲突
          if (!_isProcessingStateChange && !_isReconnecting) {
            _schedulePositionCheck(event);
          }
        }
      },
      onError: (Object e, StackTrace stackTrace) {
        // 避免错误处理时的状态冲突
        if (_isProcessingStateChange || _isReconnecting) {
          print('⏳ AudioService: 跳过错误处理 - 正在处理其他状态');
          return;
        }
        
        print('❌ AudioService: 播放事件流错误 - $e');
        print('📍 AudioService: 错误类型 - ${e.runtimeType}');
        
        // 分析错误并提供解决建议
        final errorAnalysis = _analyzeGeneralError(e);
        print('🔬 错误分析: $errorAnalysis');
        
        _updatePlaybackState(null, error: errorAnalysis);
      },
    );

    print('✅ AudioService: 音频服务初始化完成');
  }

  /// 播放电台
  Future<void> playStation(StationSimple station) async {
    print('🎵 AudioService: 开始播放电台');
    print('📻 电台名称: ${station.name}');
    print('🔗 电台URL: ${station.url}');
    print('🌍 电台国家: ${station.country}');
    print('📱 当前平台: ${Platform.isAndroid ? 'Android' : Platform.isIOS ? 'iOS' : 'Other'}');

    try {
      // BUG修复: 重置手动暂停标志，开始新的播放会话
      _isManualPause = false;
      // 立即更新当前电台和加载状态
      _currentStation = station;
      if (_ref != null) {
        _ref!.read(currentStationProvider.notifier).state = station;
      }
      _updatePlaybackState(null, state: PlaybackState.loading);

      // 添加到播放历史
      final storageService = _storageService;
      if (storageService != null) {
        print('📝 AudioService: 添加电台到播放历史: ${station.name}');
        await storageService.addToPlayHistory(station);
        print('✅ AudioService: 成功添加到播放历史');

        // 刷新最近播放列表
        if (_ref != null) {
          try {
            _ref!.read(recentStationsProvider.notifier).refresh();
            print('🔄 AudioService: 最近播放列表已刷新');
          } catch (e) {
            print('⚠️ AudioService: 刷新最近播放列表失败: $e');
          }
        }
      } else {
        print('⚠️ AudioService: StorageService为空，无法记录播放历史');
      }

      // 详细记录URL设置过程
      print('🔧 AudioService: 开始设置音频源URL...');
      print('🔗 AudioService: 目标URL: ${station.url}');

      // 检查URL格式
      final uri = Uri.tryParse(station.url);
      if (uri == null) {
        throw Exception('无效的URL格式: ${station.url}');
      }

      print('🔍 AudioService: URL解析结果:');
      print('   - 协议: ${uri.scheme}');
      print('   - 主机: ${uri.host}');
      print('   - 端口: ${uri.port}');
      print('   - 路径: ${uri.path}');

      // 检查是否为播放列表文件并解析
      String actualStreamUrl = station.url;
      if (_isPlaylistFile(station.url)) {
        print('📋 AudioService: 检测到播放列表文件，开始解析...');
        try {
          actualStreamUrl = await _parsePlaylistFile(station.url);
          print('✅ AudioService: 播放列表解析成功');
          print('🔗 AudioService: 实际流URL: $actualStreamUrl');
        } catch (e) {
          print('❌ AudioService: 播放列表解析失败: $e');
          throw Exception('播放列表解析失败: $e');
        }
      } else {
        print('📺 AudioService: 直接使用原始URL播放（M3U8 HLS流或直播流）');
        print('🔗 AudioService: 直播流URL: $actualStreamUrl');
      }

      // 设置音频源并开始播放
      final stopwatch = Stopwatch()..start();
      await _player.setUrl(actualStreamUrl);
      stopwatch.stop();

      print('✅ AudioService: 音频源设置完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
      print('🎵 AudioService: 开始播放...');

      await _player.play();
      print('✅ AudioService: 播放命令已发送');

      // 播放状态将通过playerStateStream自动更新
      // 不需要手动设置为playing状态

    } catch (e, stackTrace) {
      print('❌ AudioService: 播放电台时发生错误');
      print('🔍 错误详情: $e');
      print('📍 错误类型: ${e.runtimeType}');
      print('📚 错误堆栈: $stackTrace');

      // 分析具体错误类型
      String detailedError = _analyzeError(e, station.url);
      print('🔬 错误分析: $detailedError');

      _updatePlaybackState(null,
        state: PlaybackState.error,
        error: detailedError
      );
    }
  }

  /// 带播放列表上下文的播放电台方法
  /// 
  /// 功能实现: 支持播放列表上下文管理的电台播放
  /// 实现方案: 扩展现有播放方法，添加播放列表上下文记录
  /// 影响范围: audio_service.dart
  /// 实现日期: 2025-01-27
  Future<void> playStationWithPlaylist({
    required StationSimple station,
    required PlaylistContext playlistContext,
  }) async {
    print('🎵 AudioService: 开始播放电台（带播放列表）');
    print('📻 电台名称: ${station.name}');
    print('📋 播放列表: ${playlistContext.sourceTitle} (${playlistContext.currentIndex + 1}/${playlistContext.stations.length})');
    
    // 更新播放列表上下文
    _currentPlaylist = playlistContext;
    
    // 更新Provider状态
    if (_ref != null) {
      _ref!.read(currentPlaylistProvider.notifier).state = playlistContext;
    }
    
    // 调用原有的播放方法
    await playStation(station);
    
    print('✅ AudioService: 播放列表上下文已设置');
  }

  /// 播放上一首电台
  /// 
  /// 功能实现: 在当前播放列表中切换到上一首电台
  /// 实现方案: 基于当前播放列表上下文切换到上一首
  /// 影响范围: audio_service.dart
  /// 实现日期: 2025-01-27
  Future<bool> playPrevious() async {
    print('⏮️ AudioService: 尝试播放上一首');
    
    if (_currentPlaylist == null) {
      print('❌ AudioService: 没有播放列表上下文');
      return false;
    }
    
    final previousContext = _currentPlaylist!.switchToPrevious();
    if (previousContext == null) {
      print('❌ AudioService: 已经是第一首，无法播放上一首');
      return false;
    }
    
    final previousStation = previousContext.currentStation;
    print('⏮️ AudioService: 切换到上一首: ${previousStation.name}');
    
    await playStationWithPlaylist(
      station: previousStation,
      playlistContext: previousContext,
    );
    
    return true;
  }

  /// 播放下一首电台
  /// 
  /// 功能实现: 在当前播放列表中切换到下一首电台
  /// 实现方案: 基于当前播放列表上下文切换到下一首
  /// 影响范围: audio_service.dart
  /// 实现日期: 2025-01-27
  Future<bool> playNext() async {
    print('⏭️ AudioService: 尝试播放下一首');
    
    if (_currentPlaylist == null) {
      print('❌ AudioService: 没有播放列表上下文');
      return false;
    }
    
    final nextContext = _currentPlaylist!.switchToNext();
    if (nextContext == null) {
      print('❌ AudioService: 已经是最后一首，无法播放下一首');
      return false;
    }
    
    final nextStation = nextContext.currentStation;
    print('⏭️ AudioService: 切换到下一首: ${nextStation.name}');
    
    await playStationWithPlaylist(
      station: nextStation,
      playlistContext: nextContext,
    );
    
    return true;
  }

  /// 清除播放列表上下文
  /// 
  /// 功能实现: 清除当前的播放列表上下文
  /// 实现方案: 重置播放列表相关状态
  /// 影响范围: audio_service.dart
  /// 实现日期: 2025-01-27
  void clearPlaylist() {
    print('🗑️ AudioService: 清除播放列表上下文');
    _currentPlaylist = null;
    
    // 更新Provider状态
    if (_ref != null) {
      _ref!.read(currentPlaylistProvider.notifier).state = null;
    }
  }

  /// 暂停播放
  Future<void> pause() async {
    print('⏸️ AudioService: 暂停播放');
    try {
      // BUG修复: 设置手动暂停标志，避免触发自动恢复
      _isManualPause = true;
      await _player.pause();
      print('✅ AudioService: 暂停成功');
    } catch (e) {
      print('❌ AudioService: 暂停失败 - $e');
    }
  }

  /// 恢复播放
  Future<void> resume() async {
    print('▶️ AudioService: 恢复播放');
    try {
      // BUG修复: 重置手动暂停标志，恢复正常状态检测
      _isManualPause = false;
      await _player.play();
      print('✅ AudioService: 恢复播放成功');
    } catch (e) {
      print('❌ AudioService: 恢复播放失败 - $e');
    }
  }

  /// 停止播放
  Future<void> stop() async {
    print('⏹️ AudioService: 停止播放');
    try {
      // BUG修复: 重置手动暂停标志，停止播放会话
      _isManualPause = false;
      await _player.stop();
      _currentStation = null;
      // 更新StateProvider
      if (_ref != null) {
        _ref!.read(currentStationProvider.notifier).state = null;
      }
      _updatePlaybackState(null, state: PlaybackState.stopped);
      print('✅ AudioService: 停止播放成功');
    } catch (e) {
      print('❌ AudioService: 停止播放失败 - $e');
    }
  }

  /// 设置音量 (0.0 - 1.0)
  Future<void> setVolume(double volume) async {
    final clampedVolume = volume.clamp(0.0, 1.0);
    print('🔊 AudioService: 设置音量 $volume -> $clampedVolume');
    try {
      await _player.setVolume(clampedVolume);
      print('✅ AudioService: 音量设置成功');
    } catch (e) {
      print('❌ AudioService: 音量设置失败 - $e');
    }
  }

  /// 强制刷新播放状态（用于修复状态同步问题）
  void refreshPlaybackState() {
    _emitCurrentPlayback();
  }

  /// 检查URL是否为播放列表文件
  bool _isPlaylistFile(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    final path = uri.path.toLowerCase();
    final fullUrl = url.toLowerCase();
    
    // M3U8文件通常是HLS流，应该直接播放，不需要解析
    if (path.endsWith('.m3u8') || fullUrl.contains('.m3u8')) {
      print('📺 AudioService: 检测到M3U8 HLS流，将直接播放: $url');
      return false; // M3U8 HLS流直接播放
    }
    
    return path.endsWith('.pls') ||
           path.endsWith('.m3u') ||
           path.contains('.pls') ||
           path.contains('.m3u');
  }

  /// 解析播放列表文件获取实际流URL
  Future<String> _parsePlaylistFile(String playlistUrl) async {
    print('📋 AudioService: 开始下载播放列表文件: $playlistUrl');

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 10);
      dio.options.receiveTimeout = const Duration(seconds: 10);

      final response = await dio.get(playlistUrl);
      final content = response.data.toString();

      print('📋 AudioService: 播放列表内容长度: ${content.length}');
      print('📋 AudioService: 播放列表内容预览: ${content.substring(0, content.length > 200 ? 200 : content.length)}');

      if (playlistUrl.toLowerCase().contains('.pls')) {
        return _parsePlsFile(content);
      } else if (playlistUrl.toLowerCase().contains('.m3u')) {
        return _parseM3uFile(content);
      } else {
        // 尝试自动检测格式
        if (content.contains('[playlist]')) {
          return _parsePlsFile(content);
        } else {
          return _parseM3uFile(content);
        }
      }
    } catch (e) {
      print('❌ AudioService: 下载播放列表失败: $e');
      rethrow;
    }
  }

  /// 解析PLS格式播放列表
  String _parsePlsFile(String content) {
    print('📋 AudioService: 解析PLS格式播放列表');

    final lines = content.split('\n');
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.startsWith('File1=') || trimmedLine.startsWith('file1=')) {
        final url = trimmedLine.substring(6);
        print('📋 AudioService: 找到PLS流URL: $url');
        return url;
      }
    }

    throw Exception('PLS文件中未找到有效的流URL');
  }

  /// 解析M3U格式播放列表
  String _parseM3uFile(String content) {
    print('📋 AudioService: 解析M3U格式播放列表');

    final lines = content.split('\n');
    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isNotEmpty &&
          !trimmedLine.startsWith('#') &&
          (trimmedLine.startsWith('http://') || trimmedLine.startsWith('https://'))) {
        print('📋 AudioService: 找到M3U流URL: $trimmedLine');
        return trimmedLine;
      }
    }

    throw Exception('M3U文件中未找到有效的流URL');
  }

  /// 分析播放错误的具体原因
  String _analyzeError(Object error, String url) {
    final errorString = error.toString().toLowerCase();
    final uri = Uri.tryParse(url);

    print('🔬 AudioService: 开始分析错误...');
    print('🔍 错误字符串: $errorString');

    // 网络连接相关错误
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('unreachable')) {
      print('🌐 错误类型: 网络连接问题');
      return '网络连接失败 - 请检查网络连接或电台服务器状态';
    }

    // HTTP相关错误
    if (errorString.contains('http') ||
        errorString.contains('404') ||
        errorString.contains('403') ||
        errorString.contains('500')) {
      print('🌐 错误类型: HTTP错误');
      return 'HTTP请求失败 - 电台服务器返回错误响应';
    }

    // SSL/TLS相关错误
    if (errorString.contains('ssl') ||
        errorString.contains('tls') ||
        errorString.contains('certificate')) {
      print('🔒 错误类型: SSL/TLS错误');
      return 'SSL证书验证失败 - 服务器证书问题';
    }

    // 音频格式相关错误
    if (errorString.contains('format') ||
        errorString.contains('codec') ||
        errorString.contains('unsupported') ||
        errorString.contains('unrecognizedinputformatexception') ||
        errorString.contains('extractors') ||
        errorString.contains('could read the stream')) {
      print('🎵 错误类型: 音频格式问题');

      // 检查是否为播放列表文件问题
      if (_isPlaylistFile(url)) {
        return '播放列表文件格式问题 - 可能是PLS/M3U文件解析失败或包含无效的流URL';
      }

      return '不支持的音频格式 - 该电台使用了不兼容的音频编码';
    }

    // Android特有的网络安全策略错误
    if (Platform.isAndroid &&
        (errorString.contains('cleartext') ||
         errorString.contains('security') ||
         errorString.contains('policy'))) {
      print('🤖 错误类型: Android网络安全策略');
      return 'Android网络安全策略限制 - HTTP连接被阻止';
    }

    // URL相关错误
    if (uri == null || uri.host.isEmpty) {
      print('🔗 错误类型: URL格式错误');
      return 'URL格式错误 - 电台链接无效';
    }

    // 端口相关问题
    if (uri.hasPort && (uri.port < 1 || uri.port > 65535)) {
      print('🔌 错误类型: 端口号错误');
      return '端口号错误 - 电台使用了无效的端口号';
    }

    // 平台特定分析
    if (Platform.isAndroid) {
      print('🤖 Android平台特定分析:');
      print('   - 检查网络安全配置是否允许该域名');
      print('   - 检查是否需要添加域名到白名单');
      return 'Android播放失败 - 可能是网络安全配置限制';
    } else if (Platform.isIOS) {
      print('🍎 iOS平台特定分析:');
      print('   - 检查App Transport Security设置');
      print('   - 检查网络权限配置');
      return 'iOS播放失败 - 可能是网络权限或ATS设置问题';
    }

    // 通用错误
    print('❓ 错误类型: 未知错误');
    return '播放失败: $error';
  }

  /// 更新播放状态（增加状态保护机制）
  void _updatePlaybackState(
    PlayerState? playerState, {
    PlaybackState? state,
    String? error,
  }) {
    // 避免并发状态更新
    if (_isProcessingStateChange) {
      print('⏳ AudioService: 跳过状态更新 - 正在处理其他状态变化');
      return;
    }
    
    _isProcessingStateChange = true;
    
    try {
      PlaybackState newState = _currentState;
      final oldState = _currentState;

      if (state != null) {
        newState = state;
        print('🔄 AudioService: 手动设置状态 ${oldState.name} -> ${newState.name}');
      } else if (playerState != null) {
        print('🔄 AudioService: 播放器状态更新 - ${playerState.processingState.name}, playing: ${playerState.playing}');

        switch (playerState.processingState) {
          case ProcessingState.idle:
            newState = PlaybackState.stopped;
            break;
          case ProcessingState.loading:
          case ProcessingState.buffering:
            newState = PlaybackState.loading;
            break;
          case ProcessingState.ready:
            newState = playerState.playing
                ? PlaybackState.playing
                : PlaybackState.paused;
            break;
          case ProcessingState.completed:
            newState = PlaybackState.stopped;
            break;
        }

        if (newState != oldState) {
          print('📊 AudioService: 状态变化 ${oldState.name} -> ${newState.name}');
        }
      }

      // 只有状态真正改变时才更新
      if (newState != _currentState) {
        _currentState = newState;
        print('✅ AudioService: 状态已更新为 ${newState.name}');
        
        // 播放成功时，延迟重置停滞计数（避免HLS片段切换时的误重置）
        if (newState == PlaybackState.playing) {
          print('🔄 AudioService: 播放状态恢复');
          // 延迟重置，确保播放真正稳定
          Timer(Duration(seconds: 10), () {
            if (_currentState == PlaybackState.playing) {
              _stallCount = 0;
              _lastStallTime = null;
              print('✅ AudioService: 播放稳定，重置停滞计数');
            }
          });
        }
        
        _emitCurrentPlayback(error: error);
      } else if (error != null) {
        // 即使状态没变，如果有错误也要发送
        print('❌ AudioService: 发送错误信息 - $error');
        _emitCurrentPlayback(error: error);
      }
    } finally {
      _isProcessingStateChange = false;
    }
  }

  /// 发送当前播放状态
  void _emitCurrentPlayback({
    Duration? position,
    String? error,
  }) {
    final currentPlayback = CurrentPlayback(
      station: _currentStation != null 
          ? null // 暂时不包含完整的RadioStation对象
          : null,
      state: _currentState,
      position: position ?? _player.position,
      errorMessage: error,
    );

    _playbackController.add(currentPlayback);
  }

  /// 尝试自动恢复播放
  /// 
  /// 功能实现: 检测到意外暂停时，尝试自动恢复播放
  /// 实现方案: 延迟重试机制，避免频繁重试
  /// 影响范围: audio_service.dart
  /// 实现日期: 2025-01-27
  void _attemptAutoResume() {
    if (_currentStation == null || _retryCount >= _maxRetries) {
      if (_retryCount >= _maxRetries) {
        print('❌ AudioService: 已达到最大重试次数，停止自动恢复');
        _retryCount = 0; // 重置重试计数
      }
      return;
    }

    _retryCount++;
    print('🔄 AudioService: 尝试自动恢复播放 (第${_retryCount}次)');

    // 取消之前的重试定时器
    _retryTimer?.cancel();

    // 延迟2秒后重试，避免过于频繁
    _retryTimer = Timer(Duration(seconds: 2), () async {
      try {
        print('🎵 AudioService: 执行自动恢复播放');
        // BUG修复: 自动恢复时确保不是手动暂停状态
        _isManualPause = false;
        await _player.play();
        
        // 如果恢复成功，重置重试计数
        _retryCount = 0;
        print('✅ AudioService: 自动恢复播放成功');
      } catch (e) {
        print('❌ AudioService: 自动恢复播放失败 - $e');
        
        // 如果简单恢复失败，尝试重新加载音频源
        if (_retryCount < _maxRetries) {
          print('🔄 AudioService: 尝试重新加载音频源');
          _attemptReloadAudioSource();
        }
      }
    });
  }

  /// 尝试重新加载音频源
  void _attemptReloadAudioSource() {
    if (_currentStation == null) return;

    Timer(Duration(seconds: 3), () async {
      try {
        print('🔄 AudioService: 重新加载音频源');
        await playStation(_currentStation!);
      } catch (e) {
        print('❌ AudioService: 重新加载音频源失败 - $e');
      }
    });
  }

  /// 处理HLS流播放问题
  /// 
  /// 功能实现: 检测到HLS流位置异常时的处理逻辑
  /// 实现方案: 重新设置音频源，避免片段连接问题，增加状态保护
  /// 影响范围: audio_service.dart - HLS流播放
  /// 实现日期: 2025-01-27
  void _handleHlsPlaybackIssue() async {
    if (_currentStation == null || _isReconnecting) {
      print('⏳ AudioService: 跳过HLS重连 - 无电台或正在重连中');
      return;
    }
    
    _isReconnecting = true;
    print('🔄 AudioService: 开始处理HLS流播放问题');
    
    try {
      // 记录当前播放位置
      final currentPosition = _player.position;
      print('📍 AudioService: 当前播放位置: $currentPosition');
      
      // 重新设置音频源（这会刷新HLS流连接）
      print('🔄 AudioService: 重新设置HLS音频源');
      await _player.setUrl(_currentStation!.url);
      
      // 对于HLS直播流，不进行位置跳转以避免重复播放
      // HLS流会自动处理片段连接，强制跳转会导致重复播放
      print('ℹ️ AudioService: HLS流重连 - 跳过位置跳转，让流自然继续');
      
      // 继续播放
      await _player.play();
      print('✅ AudioService: HLS流重连完成');
      
    } catch (e) {
      print('❌ AudioService: HLS流重连失败: $e');
      // 避免状态冲突：不在重连过程中触发状态更新
      if (!_isProcessingStateChange) {
        _updatePlaybackState(null, error: '流媒体连接中断，正在重试...');
      }
    } finally {
      _isReconnecting = false;
    }
  }
  
  /// 调度停滞恢复
  /// 
  /// 功能实现: 检测到播放停滞时的延迟恢复机制
  /// 实现方案: 延迟处理避免频繁重试，智能恢复策略
  /// 影响范围: audio_service.dart - 播放恢复
  /// 实现日期: 2025-01-27
  void _scheduleStallRecovery() {
    final now = DateTime.now();
    
    // 避免过于频繁的恢复尝试
    if (_lastStallTime != null && 
        now.difference(_lastStallTime!).inSeconds < 5) {
      print('⏳ AudioService: 恢复尝试过于频繁，跳过此次');
      return;
    }
    
    _lastStallTime = now;
    _stallCount++;
    
    print('🔄 AudioService: 调度停滞恢复 (第${_stallCount}次)');
    
    // 取消之前的恢复定时器
    _stallRecoveryTimer?.cancel();
    
    // 如果已达到最大重试次数，停止尝试
    if (_stallCount >= _maxStallRetries) {
      print('❌ AudioService: 已达到最大停滞恢复次数，停止重试');
      _updatePlaybackState(null, error: '播放连接不稳定，请稍后重试');
      _stallCount = 0; // 重置计数
      return;
    }
    
    // 延迟处理：HLS流需要更长时间，普通流较短时间
    final delaySeconds = (_currentStation!.url.toLowerCase().contains('.m3u8')) ? 8 : 3;
    print('⏳ AudioService: 将在${delaySeconds}秒后尝试恢复');
    
    _stallRecoveryTimer = Timer(Duration(seconds: delaySeconds), () async {
      if (_currentStation == null) return;
      
      print('🎯 AudioService: 执行停滞恢复');
      
      try {
        // 对于HLS流，尝试重新连接
        if (_currentStation!.url.toLowerCase().contains('.m3u8')) {
          _handleHlsPlaybackIssue();
        } else {
          // 对于其他格式，简单重试播放
          await _player.play();
          print('✅ AudioService: 非HLS流恢复成功');
        }
        
        // 重置停滞计数（恢复成功）
        _stallCount = 0;
        
      } catch (e) {
        print('❌ AudioService: 停滞恢复失败: $e');
      }
    });
  }

  /// 分析一般播放错误
  /// 
  /// 功能实现: 分析各种播放错误类型并提供解决建议
  /// 实现方案: 基于错误字符串和类型进行智能分析
  /// 影响范围: audio_service.dart - 错误处理
  /// 实现日期: 2025-01-27
  String _analyzeGeneralError(Object e) {
    print('🔬 AudioService: 分析播放错误...');
    print('   - 错误类型: ${e.runtimeType}');
    print('   - 错误内容: $e');
    
    final errorString = e.toString().toLowerCase();
    
    // Controller 状态错误 (我们遇到的具体问题)
    if (errorString.contains('cannot fire new event') || 
        errorString.contains('controller is already firing')) {
      return 'HLS流状态冲突 - 正在自动处理，请稍候';
    }
    
    // 网络相关错误
    if (errorString.contains('network') || errorString.contains('connection') || 
        errorString.contains('timeout') || errorString.contains('could not connect') ||
        errorString.contains('unreachable')) {
      return 'HLS流网络连接失败 - 请检查网络连接或稍后重试';
    }
    
    // HLS特定错误
    if (errorString.contains('hls') || errorString.contains('m3u8') || 
        errorString.contains('playlist') || errorString.contains('manifest')) {
      return 'HLS流解析失败 - 电台流格式或服务器问题';
    }
    
    // 媒体格式错误
    if (errorString.contains('format') || errorString.contains('codec') || 
        errorString.contains('unsupported')) {
      return '不支持的音频格式 - 该电台使用了不兼容的编码';
    }
    
    // 权限或安全错误
    if (errorString.contains('permission') || errorString.contains('security') || 
        errorString.contains('cors')) {
      return '访问权限错误 - 电台服务器拒绝访问';
    }
    
    // 播放器中断错误
    if (errorString.contains('interrupted') || errorString.contains('abort')) {
      return '播放被中断 - 正在重新连接';
    }
    
    // 通用错误
    return '播放错误 - $errorString';
  }
  
  /// 延迟位置检查
  /// 
  /// 功能实现: 避免在状态变化时立即处理位置异常
  /// 实现方案: 延迟检查，避免并发状态操作
  /// 影响范围: audio_service.dart - 状态管理
  /// 实现日期: 2025-01-27
  void _schedulePositionCheck(PlaybackEvent event) {
    if (_currentStation == null) return;
    
    // 只对 HLS 流进行位置检查
    if (_currentStation!.url.toLowerCase().contains('.m3u8') != true) {
      return;
    }
    
    print('📅 AudioService: 调度位置异常检查');
    
    // 延迟 2 秒后检查，避免状态冲突
    Timer(Duration(seconds: 2), () {
      // 确保没有其他操作在进行
      if (_isProcessingStateChange || _isReconnecting) {
        print('⏳ AudioService: 跳过位置检查 - 正在处理其他状态');
        return;
      }
      
      if (_currentStation == null || _currentState != PlaybackState.playing) {
        print('⏳ AudioService: 跳过位置检查 - 播放状态已变化');
        return;
      }
      
      final positionSeconds = event.updatePosition.inSeconds;
      final durationSeconds = event.duration?.inSeconds ?? 0;
      
      if (positionSeconds > durationSeconds + 10) { // 提高阈值到10秒
        print('🔄 AudioService: 确认HLS流位置严重异常，需要重连');
        _handleHlsPlaybackIssue();
      } else {
        print('ℹ️ AudioService: HLS流位置异常在可接受范围内');
      }
    });
  }

  /// 初始化媒体控制
  ///
  /// 功能实现: 初始化系统媒体控制服务
  /// 实现方案: 集成 audio_service 提供锁屏媒体控制
  /// 影响范围: audio_service.dart
  /// 实现日期: 2025-01-27
  Future<void> _initializeMediaControls() async {
    if (_mediaControlsInitialized) {
      print('✅ AudioService: 媒体控制已初始化，跳过');
      return;
    }

    try {
      print('🎵 AudioService: 初始化媒体控制服务');

      // 注意：MediaControlService 的初始化将在应用启动时通过 Provider 系统处理
      // 这里只是标记媒体控制已准备就绪
      _mediaControlsInitialized = true;

      print('✅ AudioService: 媒体控制服务初始化完成');
    } catch (e) {
      print('❌ AudioService: 媒体控制服务初始化失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _retryTimer?.cancel();
    _stallRecoveryTimer?.cancel();
    _player.dispose();
    _playbackController.close();
  }
}

/// Riverpod提供者
final audioServiceProvider = Provider<AudioService>((ref) {
  return AudioService();
});

/// 当前播放状态提供者
final currentPlaybackProvider = StreamProvider<CurrentPlayback>((ref) {
  final audioService = ref.watch(audioServiceProvider);
  return audioService.playbackStream;
});

/// 当前播放电台提供者
final currentStationProvider = StateProvider<StationSimple?>((ref) {
  return null;
});

/// 当前播放列表上下文提供者
/// 
/// 功能实现: 管理当前播放列表的上下文状态
/// 实现方案: 使用StateProvider管理播放列表上下文
/// 影响范围: audio_service.dart, player_modal.dart
/// 实现日期: 2025-01-27
final currentPlaylistProvider = StateProvider<PlaylistContext?>((ref) {
  return null;
});
