import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/models.dart';
import 'audio_service.dart' as app_audio;

/// 媒体控制服务
/// 
/// 负责管理系统媒体控制和锁屏播放器集成
/// 功能实现: 集成 audio_service 提供锁屏媒体控制
/// 实现方案: 继承 BaseAudioHandler 处理系统媒体事件
/// 影响范围: 全局媒体控制，锁屏播放器
/// 实现日期: 2025-01-27
class MediaControlService extends BaseAudioHandler with <PERSON>ueHandler, SeekHandler {
  MediaControlService({
    required this.audioService,
    required this.ref,
  }) {
    _init();
  }

  final app_audio.AudioService audioService;
  final Ref ref;
  
  StreamSubscription<CurrentPlayback>? _playbackSubscription;
  StreamSubscription<StationSimple?>? _stationSubscription;
  StreamSubscription<PlaylistContext?>? _playlistSubscription;

  /// 初始化媒体控制服务
  void _init() {
    print('🎵 MediaControlService: 初始化媒体控制服务');
    
    // 监听播放状态变化
    _playbackSubscription = audioService.playbackStream.listen(_onPlaybackChanged);
    
    // 监听当前电台变化
    _stationSubscription = ref.read(currentStationProvider.notifier).stream.listen(_onStationChanged);
    
    // 监听播放列表变化
    _playlistSubscription = ref.read(currentPlaylistProvider.notifier).stream.listen(_onPlaylistChanged);
    
    print('✅ MediaControlService: 媒体控制服务初始化完成');
  }

  /// 处理播放状态变化
  void _onPlaybackChanged(CurrentPlayback playback) {
    print('🎵 MediaControlService: 播放状态变化 - ${playback.state}');
    
    // 更新播放状态
    switch (playback.state) {
      case app_audio.PlaybackState.playing:
        playbackState.add(playbackState.value.copyWith(
          playing: true,
          processingState: AudioProcessingState.ready,
        ));
        break;
      case app_audio.PlaybackState.paused:
        playbackState.add(playbackState.value.copyWith(
          playing: false,
          processingState: AudioProcessingState.ready,
        ));
        break;
      case app_audio.PlaybackState.loading:
        playbackState.add(playbackState.value.copyWith(
          playing: false,
          processingState: AudioProcessingState.loading,
        ));
        break;
      case app_audio.PlaybackState.stopped:
        playbackState.add(playbackState.value.copyWith(
          playing: false,
          processingState: AudioProcessingState.idle,
        ));
        break;
      case app_audio.PlaybackState.error:
        playbackState.add(playbackState.value.copyWith(
          playing: false,
          processingState: AudioProcessingState.error,
        ));
        break;
    }
    
    // 更新播放位置
    playbackState.add(playbackState.value.copyWith(
      updatePosition: playback.position,
    ));
  }

  /// 处理当前电台变化
  void _onStationChanged(StationSimple? station) {
    if (station == null) {
      print('🎵 MediaControlService: 清除媒体项');
      mediaItem.add(null);
      return;
    }
    
    print('🎵 MediaControlService: 更新媒体项 - ${station.name}');
    
    final mediaItemData = MediaItem(
      id: station.id,
      title: station.name,
      artist: 'WorldTune',
      album: station.country.isNotEmpty ? station.country : 'Radio Station',
      duration: null, // 电台流没有固定时长
      artUri: station.favicon.isNotEmpty ? Uri.tryParse(station.favicon) : null,
      extras: {
        'url': station.url,
        'language': station.language,
        'homepage': station.homepage,
        'tags': station.tags,
      },
    );
    
    mediaItem.add(mediaItemData);
  }

  /// 处理播放列表变化
  void _onPlaylistChanged(PlaylistContext? playlist) {
    if (playlist == null) {
      print('🎵 MediaControlService: 清除播放队列');
      queue.add([]);

      // 更新播放状态，禁用上一首/下一首按钮
      playbackState.add(playbackState.value.copyWith(
        queueIndex: null,
        controls: _getBasicControls(),
      ));
      return;
    }

    print('🎵 MediaControlService: 更新播放队列 - ${playlist.sourceTitle} (${playlist.currentIndex + 1}/${playlist.stations.length})');

    // 将播放列表转换为媒体项队列
    final queueItems = playlist.stations.map((station) => MediaItem(
      id: station.id,
      title: station.name,
      artist: 'WorldTune',
      album: station.country.isNotEmpty ? station.country : 'Radio Station',
      duration: null,
      artUri: station.favicon.isNotEmpty ? Uri.tryParse(station.favicon) : null,
      extras: {
        'url': station.url,
        'language': station.language,
        'homepage': station.homepage,
        'tags': station.tags,
      },
    )).toList();

    queue.add(queueItems);

    // 更新当前播放索引和控制按钮
    playbackState.add(playbackState.value.copyWith(
      queueIndex: playlist.currentIndex,
      controls: _getPlaylistControls(playlist),
    ));
  }

  /// 获取基础控制按钮（无播放列表时）
  List<MediaControl> _getBasicControls() {
    return [
      MediaControl.play,
      MediaControl.pause,
      MediaControl.stop,
    ];
  }

  /// 获取播放列表控制按钮
  List<MediaControl> _getPlaylistControls(PlaylistContext playlist) {
    final controls = <MediaControl>[];

    // 上一首按钮（仅在可用时添加）
    if (playlist.canPlayPrevious) {
      controls.add(MediaControl.skipToPrevious);
    }

    // 播放/暂停按钮
    controls.addAll([
      MediaControl.play,
      MediaControl.pause,
    ]);

    // 下一首按钮（仅在可用时添加）
    if (playlist.canPlayNext) {
      controls.add(MediaControl.skipToNext);
    }

    // 停止按钮
    controls.add(MediaControl.stop);

    return controls;
  }

  @override
  Future<void> play() async {
    print('🎵 MediaControlService: 收到播放命令');
    
    final currentStation = ref.read(currentStationProvider);
    if (currentStation == null) {
      print('❌ MediaControlService: 没有当前电台，无法播放');
      return;
    }
    
    final playback = await ref.read(currentPlaybackProvider.future);
    
    switch (playback.state) {
      case app_audio.PlaybackState.paused:
        print('📻 MediaControlService: 恢复播放');
        await audioService.resume();
        break;
      case app_audio.PlaybackState.stopped:
      case app_audio.PlaybackState.error:
        print('📻 MediaControlService: 重新播放');
        await audioService.playStation(currentStation);
        break;
      case app_audio.PlaybackState.playing:
      case app_audio.PlaybackState.loading:
        print('📻 MediaControlService: 当前状态不需要播放操作 - ${playback.state}');
        break;
    }
  }

  @override
  Future<void> pause() async {
    print('🎵 MediaControlService: 收到暂停命令');
    await audioService.pause();
  }

  @override
  Future<void> stop() async {
    print('🎵 MediaControlService: 收到停止命令');
    await audioService.stop();
  }

  @override
  Future<void> skipToPrevious() async {
    print('🎵 MediaControlService: 收到上一首命令');

    if (!audioService.canPlayPrevious) {
      print('❌ MediaControlService: 无法播放上一首 - 已经是第一首或没有播放列表');
      // 可以考虑显示用户提示，但锁屏界面通常不显示复杂提示
      return;
    }

    try {
      final success = await audioService.playPrevious();
      if (success) {
        print('✅ MediaControlService: 成功切换到上一首');
      } else {
        print('❌ MediaControlService: 播放上一首失败 - 可能已经是第一首');
      }
    } catch (e) {
      print('❌ MediaControlService: 播放上一首时发生错误: $e');
    }
  }

  @override
  Future<void> skipToNext() async {
    print('🎵 MediaControlService: 收到下一首命令');

    if (!audioService.canPlayNext) {
      print('❌ MediaControlService: 无法播放下一首 - 已经是最后一首或没有播放列表');
      // 可以考虑显示用户提示，但锁屏界面通常不显示复杂提示
      return;
    }

    try {
      final success = await audioService.playNext();
      if (success) {
        print('✅ MediaControlService: 成功切换到下一首');
      } else {
        print('❌ MediaControlService: 播放下一首失败 - 可能已经是最后一首');
      }
    } catch (e) {
      print('❌ MediaControlService: 播放下一首时发生错误: $e');
    }
  }

  @override
  Future<void> seek(Duration position) async {
    print('🎵 MediaControlService: 收到定位命令 - ${position.inSeconds}s');
    // 电台流通常不支持定位，这里只是更新显示位置
    playbackState.add(playbackState.value.copyWith(
      updatePosition: position,
    ));
  }

  @override
  Future<void> skipToQueueItem(int index) async {
    print('🎵 MediaControlService: 收到队列跳转命令 - index: $index');
    
    final playlist = ref.read(currentPlaylistProvider);
    if (playlist == null || index < 0 || index >= playlist.stations.length) {
      print('❌ MediaControlService: 无效的队列索引');
      return;
    }
    
    final targetStation = playlist.stations[index];
    final newPlaylist = playlist.switchToIndex(index);
    
    await audioService.playStationWithPlaylist(
      station: targetStation,
      playlistContext: newPlaylist,
    );
  }

  /// 释放资源
  void dispose() {
    print('🎵 MediaControlService: 释放资源');
    _playbackSubscription?.cancel();
    _stationSubscription?.cancel();
    _playlistSubscription?.cancel();
  }
}

/// MediaControlService 提供者
final mediaControlServiceProvider = Provider<MediaControlService>((ref) {
  final audioService = ref.watch(app_audio.audioServiceProvider);
  return MediaControlService(
    audioService: audioService,
    ref: ref,
  );
});
