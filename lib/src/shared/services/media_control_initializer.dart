import 'package:audio_service/audio_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'media_control_service.dart';
import 'audio_service.dart' as app_audio;

/// 媒体控制初始化器
/// 
/// 负责在应用启动时初始化系统媒体控制服务
/// 功能实现: 集成 audio_service 并注册媒体控制处理器
/// 实现方案: 在应用启动时调用 AudioService.init() 注册处理器
/// 影响范围: 全局应用启动流程
/// 实现日期: 2025-01-27
class MediaControlInitializer {
  static bool _initialized = false;
  static AudioHandler? _audioHandler;

  /// 初始化媒体控制服务
  ///
  /// 必须在应用启动时调用，通常在 main() 函数中
  static Future<void> initialize(dynamic ref) async {
    if (_initialized) {
      print('✅ MediaControlInitializer: 媒体控制已初始化，跳过');
      return;
    }

    try {
      print('🎵 MediaControlInitializer: 开始初始化媒体控制服务');

      // 获取 AudioService 和 MediaControlService 实例
      final audioService = ref.read(app_audio.audioServiceProvider);

      // 创建媒体控制处理器
      final mediaControlService = MediaControlService(
        audioService: audioService,
        ref: ref,
      );

      // 初始化 audio_service
      _audioHandler = await AudioService.init(
        builder: () => mediaControlService,
        config: const AudioServiceConfig(
          androidNotificationChannelId: 'com.worldtune.audio',
          androidNotificationChannelName: 'WorldTune Audio',
          androidNotificationChannelDescription: 'WorldTune radio playback',
          androidNotificationOngoing: true,
          androidShowNotificationBadge: true,
          androidStopForegroundOnPause: false,
          fastForwardInterval: Duration(seconds: 10),
          rewindInterval: Duration(seconds: 10),
        ),
      );

      _initialized = true;
      print('✅ MediaControlInitializer: 媒体控制服务初始化完成');

    } catch (e, stackTrace) {
      print('❌ MediaControlInitializer: 媒体控制服务初始化失败');
      print('🔍 错误详情: $e');
      print('📚 错误堆栈: $stackTrace');
      
      // 初始化失败不应该阻止应用启动，只是没有锁屏控制功能
      _initialized = false;
    }
  }

  /// 获取当前的音频处理器
  static AudioHandler? get audioHandler => _audioHandler;

  /// 检查是否已初始化
  static bool get isInitialized => _initialized;

  /// 释放资源
  static Future<void> dispose() async {
    if (_audioHandler != null && _audioHandler is MediaControlService) {
      (_audioHandler as MediaControlService).dispose();
    }
    _audioHandler = null;
    _initialized = false;
    print('🎵 MediaControlInitializer: 资源已释放');
  }
}

/// 媒体控制初始化状态提供者
final mediaControlInitializedProvider = StateProvider<bool>((ref) => false);

/// 媒体控制初始化器提供者
final mediaControlInitializerProvider = Provider<MediaControlInitializer>((ref) {
  return MediaControlInitializer();
});
