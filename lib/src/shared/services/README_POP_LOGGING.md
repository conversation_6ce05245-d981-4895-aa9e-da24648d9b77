# Pop分类请求日志功能

## 功能概述

在首页tab区域请求pop分类时，系统会自动记录请求参数和返回的电台数量，用于监控和调试pop分类的API调用情况。

## 实现原理

### 1. 标签缓存机制

在`RadioApiService`类中添加了一个标签缓存：

```dart
/// 缓存的标签信息，用于日志记录
final Map<int, String> _tagCache = {};
```

### 2. 缓存更新

当调用`getRadioTags()`方法时，会自动更新标签缓存：

```dart
// 更新标签缓存，用于日志记录
for (final tag in response.list) {
  _tagCache[tag.id] = tag.name;
}
```

### 3. Pop分类检测

在`getRadiosByCategory()`方法中，通过`_isPopCategoryRequest()`方法检测是否为pop分类请求：

```dart
/// 检查是否是pop分类请求
bool _isPopCategoryRequest(List<int> tagIds) {
  for (final tagId in tagIds) {
    final tagName = _tagCache[tagId];
    if (tagName != null && tagName.toLowerCase() == 'pop') {
      return true;
    }
  }
  return false;
}
```

### 4. 日志记录

当检测到pop分类请求时，会记录以下信息：

**请求日志：**
```
🎤 [POP分类请求] 请求参数 - tagIds: [123], countryId: 1, page: 1, pageSize: 20
```

**响应日志：**
```
🎤 [POP分类响应] 返回电台数量: 15
```

## 日志格式说明

### 请求日志
- **标识符**: `🎤 [POP分类请求]`
- **记录内容**: 
  - `tagIds`: 标签ID列表
  - `countryId`: 国家ID（可选）
  - `page`: 页码
  - `pageSize`: 每页数量

### 响应日志
- **标识符**: `🎤 [POP分类响应]`
- **记录内容**: 
  - 返回的电台数量

## 使用场景

1. **监控Pop分类使用情况**: 了解用户对pop分类的访问频率
2. **性能调试**: 监控pop分类请求的响应时间和数据量
3. **数据分析**: 分析不同国家的pop电台数量分布
4. **问题排查**: 当pop分类出现问题时，可以通过日志快速定位

## 注意事项

1. **缓存依赖**: 日志功能依赖于标签缓存，需要先调用`getRadioTags()`方法
2. **大小写不敏感**: pop分类检测使用`toLowerCase()`进行大小写不敏感匹配
3. **多标签支持**: 如果请求包含多个标签，只要其中一个是pop标签就会触发日志
4. **生产环境**: 当前使用`print()`输出日志，生产环境建议替换为专业的日志框架

## 扩展建议

1. **日志级别**: 可以根据需要调整日志级别（DEBUG、INFO等）
2. **更多分类**: 可以扩展到其他重要分类（如rock、news等）
3. **性能监控**: 可以添加请求耗时统计
4. **日志持久化**: 可以将日志保存到文件或发送到日志服务器

## 测试方法

可以使用提供的测试文件`test_pop_logging.dart`来验证功能：

```bash
dart test_pop_logging.dart
```

测试会自动：
1. 获取标签列表
2. 查找pop标签
3. 发起pop分类请求
4. 验证日志输出
