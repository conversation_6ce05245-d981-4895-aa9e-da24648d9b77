// 导出所有数据模型
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'radio_models.dart'; // 导入RadioTag类
import 'station_simple.dart'; // 导入StationSimple类，用于PlaylistContext
export 'radio_models.dart'; // 新的API数据模型（优先）
export 'station_simple.dart';
export 'radio_station.dart'; // 旧的数据模型（已废弃，RadioStation已重命名为LegacyRadioStation）

/// 电台分类模型
class RadioCategory {
  final int id;
  final String name;
  final String displayName;
  final IconData icon;
  final String color;
  final String description;
  final List<String> tags;
  final int sortOrder;
  final bool isVisible;

  const RadioCategory({
    required this.id,
    required this.name,
    required this.displayName,
    required this.icon,
    this.color = '#2196F3',
    this.description = '',
    this.tags = const [],
    this.sortOrder = 0,
    this.isVisible = true,
  });

  /// 获取本地化的显示名称
  String getLocalizedDisplayName() {
    // 尝试使用 tag_ 前缀的翻译key
    final tagKey = 'tag_${name.toLowerCase()}';
    final translated = tagKey.tr();

    // 如果翻译存在且不等于key本身，使用翻译
    if (translated != tagKey) {
      return translated;
    }

    // 回退到旧的分类翻译方式
    switch (name.toLowerCase()) {
      case 'music':
        return 'category_music'.tr();
      case 'news':
        return 'category_news'.tr();
      case 'sports':
        return 'category_sports'.tr();
      case 'entertainment':
        return 'category_entertainment'.tr();
      case 'education':
        return 'category_education'.tr();
      case 'culture':
        return 'category_culture'.tr();
      case 'religion':
        return 'category_religion'.tr();
      case 'local':
        return 'category_local'.tr();
      default:
        return displayName; // 回退到原始显示名称
    }
  }

  /// 从RadioTag创建RadioCategory的工厂方法
  static RadioCategory fromRadioTag(RadioTag tag) {
    return RadioCategory(
      id: tag.id,
      name: tag.name,
      displayName: tag.name,
      icon: _getIconForTag(tag.name),
      color: _getColorForTag(tag.name),
      description: '${tag.name}类型的电台',
      tags: [tag.name],
      sortOrder: tag.sortOrder,
      isVisible: tag.isVisible,
    );
  }

  /// 根据标签名称获取对应的图标
  static IconData _getIconForTag(String tagName) {
    switch (tagName.toLowerCase()) {
      case 'other':
        return Icons.category;
      case 'pop':
        return Icons.music_note;
      case 'music':
      case 'música':
        return Icons.library_music;
      case 'rock':
        return Icons.music_note;
      case 'news':
        return Icons.newspaper;
      case 'classical':
        return Icons.piano;
      case 'radio':
      case 'fm':
        return Icons.radio;
      case 'entretenimiento':
        return Icons.theater_comedy;
      case 'talk':
        return Icons.record_voice_over;
      case 'estación':
        return Icons.radio_button_checked;
      case 'dance':
        return Icons.music_note;
      case 'hits':
        return Icons.trending_up;
      case 'méxico':
        return Icons.flag;
      case 'oldies':
        return Icons.history;
      case '80s':
      case '90s':
      case '70s':
      case '60s':
      case '00s':
      case '50s':
      case '10s':
      case '20s':
      case '40s':
      case '30s':
        return Icons.access_time;
      case 'norteamérica':
        return Icons.public;
      case 'jazz':
        return Icons.music_note;
      case 'mol merino':
        return Icons.music_note;
      default:
        return Icons.radio;
    }
  }

  /// 根据标签名称获取对应的颜色
  static String _getColorForTag(String tagName) {
    switch (tagName.toLowerCase()) {
      case 'other':
        return '#9E9E9E';
      case 'pop':
        return '#FF6B6B';
      case 'music':
      case 'música':
        return '#2196F3';
      case 'rock':
        return '#795548';
      case 'news':
        return '#4ECDC4';
      case 'classical':
        return '#8E44AD';
      case 'radio':
      case 'fm':
        return '#FF9800';
      case 'entretenimiento':
        return '#F39C12';
      case 'talk':
        return '#607D8B';
      case 'estación':
        return '#009688';
      case 'dance':
        return '#E91E63';
      case 'hits':
        return '#FF5722';
      case 'méxico':
        return '#4CAF50';
      case 'oldies':
        return '#795548';
      case '80s':
        return '#9C27B0';
      case '90s':
        return '#673AB7';
      case '70s':
        return '#3F51B5';
      case '60s':
        return '#2196F3';
      case '00s':
        return '#00BCD4';
      case '50s':
        return '#009688';
      case '10s':
        return '#4CAF50';
      case '20s':
        return '#8BC34A';
      case '40s':
        return '#CDDC39';
      case '30s':
        return '#FFEB3B';
      case 'norteamérica':
        return '#34495E';
      case 'jazz':
        return '#6A1B9A';
      case 'mol merino':
        return '#FF6B6B';
      default:
        return '#2196F3';
    }
  }

  /// 获取主要分类列表
  static List<RadioCategory> getMainCategories() {
    return [
      const RadioCategory(
        id: 1,
        name: 'music',
        displayName: '音乐',
        icon: Icons.music_note,
        color: '#FF6B6B',
        description: '各种音乐类型的电台',
        tags: ['music', 'pop', 'rock', 'jazz', 'classical'],
        sortOrder: 1,
      ),
      const RadioCategory(
        id: 2,
        name: 'news',
        displayName: '新闻',
        icon: Icons.newspaper,
        color: '#4ECDC4',
        description: '新闻资讯和时事评论',
        tags: ['news', 'talk', 'politics', 'current affairs'],
        sortOrder: 2,
      ),
      const RadioCategory(
        id: 3,
        name: 'sports',
        displayName: '体育',
        icon: Icons.sports_soccer,
        color: '#45B7D1',
        description: '体育赛事直播和评论',
        tags: ['sports', 'football', 'basketball', 'soccer'],
        sortOrder: 3,
      ),
      const RadioCategory(
        id: 4,
        name: 'entertainment',
        displayName: '娱乐',
        icon: Icons.theater_comedy,
        color: '#F39C12',
        description: '娱乐节目和综艺',
        tags: ['entertainment', 'comedy', 'variety', 'drama'],
        sortOrder: 4,
      ),
      const RadioCategory(
        id: 5,
        name: 'education',
        displayName: '教育',
        icon: Icons.school,
        color: '#8E44AD',
        description: '教育和知识类节目',
        tags: ['education', 'science', 'history', 'learning'],
        sortOrder: 5,
      ),
      const RadioCategory(
        id: 6,
        name: 'culture',
        displayName: '文化',
        icon: Icons.palette,
        color: '#E74C3C',
        description: '文化艺术类节目',
        tags: ['culture', 'art', 'literature', 'philosophy'],
        sortOrder: 6,
      ),
      const RadioCategory(
        id: 7,
        name: 'religion',
        displayName: '宗教',
        icon: Icons.church,
        color: '#27AE60',
        description: '宗教和心灵类节目',
        tags: ['religion', 'spiritual', 'christian', 'islamic'],
        sortOrder: 7,
      ),
      const RadioCategory(
        id: 8,
        name: 'local',
        displayName: '本地',
        icon: Icons.location_city,
        color: '#34495E',
        description: '本地区域电台',
        tags: ['local', 'regional', 'community'],
        sortOrder: 8,
      ),
    ];
  }

  /// 根据标签获取分类
  static RadioCategory? getCategoryByTag(String tag) {
    final categories = getMainCategories();
    for (final category in categories) {
      if (category.tags.contains(tag.toLowerCase())) {
        return category;
      }
    }
    return null;
  }

  /// 获取分类颜色
  Color getColor() {
    try {
      return Color(int.parse(color.replaceFirst('#', '0xFF')));
    } catch (e) {
      return const Color(0xFF2196F3);
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'color': color,
      'description': description,
      'tags': tags,
      'sortOrder': sortOrder,
      'isVisible': isVisible,
    };
  }

  /// 从JSON创建实例
  factory RadioCategory.fromJson(Map<String, dynamic> json) {
    return RadioCategory(
      id: json['id'] as int,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      icon: _getIconForTag(json['name'] as String),
      color: json['color'] as String? ?? '#2196F3',
      description: json['description'] as String? ?? '',
      tags: List<String>.from(json['tags'] as List? ?? []),
      sortOrder: json['sortOrder'] as int? ?? 0,
      isVisible: json['isVisible'] as bool? ?? true,
    );
  }
}

/// 播放列表来源类型
enum PlaylistSourceType {
  /// 热门推荐
  hotRecommendations,
  /// 点击最高
  mostClicked,
  /// 高质量
  highQuality,
  /// 最新上架
  latestAdded,
  /// Tab分类网格
  categoryGrid,
  /// 播放历史
  playHistory,
  /// 收藏列表
  favorites,
}

/// 播放列表上下文
/// 
/// 记录用户点击电台时的列表信息，用于支持左右滑动切换功能
class PlaylistContext {
  final PlaylistSourceType sourceType;
  final List<StationSimple> stations;
  final int currentIndex;
  final String sourceTitle;
  final Map<String, dynamic>? sourceMetadata;

  const PlaylistContext({
    required this.sourceType,
    required this.stations,
    required this.currentIndex,
    required this.sourceTitle,
    this.sourceMetadata,
  });

  /// 获取当前播放的电台
  StationSimple get currentStation => stations[currentIndex];

  /// 是否可以播放上一首
  bool get canPlayPrevious => currentIndex > 0;

  /// 是否可以播放下一首
  bool get canPlayNext => currentIndex < stations.length - 1;

  /// 获取上一首电台
  StationSimple? get previousStation {
    if (!canPlayPrevious) return null;
    return stations[currentIndex - 1];
  }

  /// 获取下一首电台
  StationSimple? get nextStation {
    if (!canPlayNext) return null;
    return stations[currentIndex + 1];
  }

  /// 创建新的上下文，切换到指定索引
  PlaylistContext switchToIndex(int newIndex) {
    if (newIndex < 0 || newIndex >= stations.length) {
      throw ArgumentError('Index out of bounds: $newIndex');
    }
    
    return PlaylistContext(
      sourceType: sourceType,
      stations: stations,
      currentIndex: newIndex,
      sourceTitle: sourceTitle,
      sourceMetadata: sourceMetadata,
    );
  }

  /// 创建新的上下文，切换到上一首
  PlaylistContext? switchToPrevious() {
    if (!canPlayPrevious) return null;
    return switchToIndex(currentIndex - 1);
  }

  /// 创建新的上下文，切换到下一首
  PlaylistContext? switchToNext() {
    if (!canPlayNext) return null;
    return switchToIndex(currentIndex + 1);
  }

  /// 根据来源类型获取本地化标题
  static String getLocalizedSourceTitle(PlaylistSourceType sourceType) {
    switch (sourceType) {
      case PlaylistSourceType.hotRecommendations:
        return 'hot_recommendations'.tr();
      case PlaylistSourceType.mostClicked:
        return 'most_clicked'.tr();
      case PlaylistSourceType.highQuality:
        return 'high_quality'.tr();
      case PlaylistSourceType.latestAdded:
        return 'latest_added'.tr();
      case PlaylistSourceType.categoryGrid:
        return 'category_stations'.tr();
      case PlaylistSourceType.playHistory:
        return 'play_history'.tr();
      case PlaylistSourceType.favorites:
        return 'favorites'.tr();
    }
  }

  @override
  String toString() {
    return 'PlaylistContext(sourceType: $sourceType, currentIndex: $currentIndex, stationsCount: ${stations.length}, sourceTitle: $sourceTitle)';
  }
}
