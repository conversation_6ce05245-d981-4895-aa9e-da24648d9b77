import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../shared/models/models.dart';
import '../../shared/providers/radio_providers.dart';
import '../../shared/providers/country_provider.dart';
import '../../shared/providers/startup_cache_provider.dart';
import '../../shared/services/startup_cache_service.dart';
import '../../shared/services/audio_service.dart';
import '../../shared/utils/backup_radio_images.dart';
import '../../shared/utils/station_image_builder.dart';
import '../../shared/utils/startup_performance_monitor.dart';
import '../../shared/widgets/station_card.dart';

/// 首页 - Tab切换+独立推荐区域
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late AnimationController _animationController;
  late AnimationController _cardAnimationController;
  late AnimationController _slideAnimationController;
  int _selectedCategoryIndex = 0;
  List<RadioCategory> _categories = []; // 改为动态列表
  final Map<String, GlobalKey> _cardKeys = {};
  final ScrollController _scrollController = ScrollController();

  bool _isInitialized = false;
  bool _recommendationsInitialized = false; // 标志：推荐数据是否已初始化
  bool _shouldLoadDataWhenCategoriesReady = false; // 标志：分类数据准备好后是否需要加载数据
  bool _cacheDataLoaded = false; // 标志：缓存数据是否已加载
  DateTime? _lastLoadTime;

  // 保存上一次的电台数据，避免切换时空白
  List<StationSimple> _lastStations = [];
  
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // 优先加载缓存数据，然后等待国家选择完成后加载最新数据
    _loadCachedDataFirst();
    _loadInitialDataWhenReady();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _cardAnimationController.dispose();
    _slideAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 检查是否需要加载数据
  void _checkAndLoadData() {
    if (!_isInitialized) {
      _loadInitialDataWhenReady();
      _isInitialized = true;
    } else {
      // 检查数据是否过期（5分钟）
      if (_lastLoadTime == null ||
          DateTime.now().difference(_lastLoadTime!) > const Duration(minutes: 5)) {
        _loadInitialDataWhenReady();
      }
    }
  }

  /// 优先加载缓存数据（快速启动）
  void _loadCachedDataFirst() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        print('🔍 开始检查启动缓存...');

        // 等待缓存服务初始化完成
        await Future.delayed(const Duration(milliseconds: 100));

        final cacheState = ref.read(startupCacheProvider);
        final cachedData = cacheState.cacheData;

        print('🔍 缓存状态检查:');
        print('   - 缓存数据存在: ${cachedData != null}');
        print('   - 缓存已加载标志: $_cacheDataLoaded');
        print('   - 缓存有效性: ${cacheState.hasCache}');

        // 检查缓存是否对应当前选中的分类
        final currentCategoryId = _categories.isNotEmpty && _selectedCategoryIndex < _categories.length 
            ? _categories[_selectedCategoryIndex].id 
            : null;
        final isCacheValidForCategory = cachedData?.isValidForCategory(currentCategoryId) ?? false;
        
        print('   - 当前分类ID: $currentCategoryId');
        print('   - 缓存分类ID: ${cachedData?.selectedCategoryId}');
        print('   - 缓存分类匹配: $isCacheValidForCategory');

        if (cachedData != null && !_cacheDataLoaded && isCacheValidForCategory) {
          print('🚀 发现启动缓存，立即显示缓存数据');
          print('   - 首页电台数量: ${cachedData.homeStations.length}');
          print('   - 热门电台数量: ${cachedData.popularStations.length}');
          print('   - 分类数量: ${cachedData.categories.length}');
          print('   - 缓存国家: ${cachedData.selectedCountry.name}');

          _cacheDataLoaded = true;

          // 设置缓存的国家选择
          if (cachedData.selectedCountry.id != ref.read(selectedCountryProvider)?.id) {
            await ref.read(countrySelectionProvider.notifier)
                .selectCountry(cachedData.selectedCountry);
          }

          // 设置缓存的分类数据
          _categories = cachedData.categories;
          if (_categories.isNotEmpty) {
            _selectedCategoryIndex = 0;
          }

          // 立即显示缓存的首页数据
          _displayCachedHomeData(cachedData);

          // 启动动画
          _animationController.forward();
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted) {
              _cardAnimationController.forward();
            }
          });

          PerformanceTracker.mark('cached_content_displayed');
          print('✅ 缓存数据显示完成，用户可以立即看到内容');
        } else {
          print('⚠️ 没有可用的启动缓存');
          if (cachedData == null) {
            print('   - 原因: 缓存数据为空');
          }
          if (_cacheDataLoaded) {
            print('   - 原因: 缓存数据已经加载过');
          }
        }
      } catch (e) {
        print('❌ 加载缓存数据失败: $e');
      }
    });
  }

  /// 等待国家选择完成后加载初始数据
  void _loadInitialDataWhenReady() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 首先检查当前国家选择状态
      final countryState = ref.read(countrySelectionProvider);
      print('🏠 检查国家选择状态: selectedCountry=${countryState.selectedCountry?.name}, isLoading=${countryState.isLoading}');

      if (countryState.selectedCountry != null && !countryState.isLoading) {
        // 国家已选择且不在加载中，直接加载数据
        print('🏠 国家已选择，直接加载首页数据: ${countryState.selectedCountry!.name}');
        _loadInitialData();
      } else {
        // 国家还未选择或正在加载，设置监听器等待选择完成
        print('🏠 国家未选择或正在加载，设置监听器等待国家选择完成...');
        _setupCountrySelectionListener();
      }
    });
  }

  /// 设置国家选择监听器（使用定时器方式）
  void _setupCountrySelectionListener() {
    // 使用定时器定期检查国家选择状态，避免ref.listen在addPostFrameCallback中的问题
    Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      final countryState = ref.read(countrySelectionProvider);
      if (countryState.selectedCountry != null && !countryState.isLoading) {
        print('🏠 定时器检查：国家选择完成，开始加载首页数据: ${countryState.selectedCountry!.name}');
        timer.cancel(); // 取消定时器
        _loadInitialData();
      }

      // 超时保护：5秒后取消定时器
      if (timer.tick > 25) { // 25 * 200ms = 5秒
        print('🏠 国家选择监听超时，取消定时器');
        timer.cancel();
      }
    });

    // 额外添加一个延迟检查，防止错过状态变化
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;
      final countryState = ref.read(countrySelectionProvider);
      if (countryState.selectedCountry != null && !countryState.isLoading) {
        print('🏠 延迟检查：国家选择已完成，触发数据加载: ${countryState.selectedCountry!.name}');
        _loadInitialData();
      }
    });
  }



  /// 在build时检查并加载数据（防止错过初始化时机）
  void _checkAndLoadDataOnBuild() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final countryState = ref.read(countrySelectionProvider);
      final homeTabState = ref.read(homeTabRadiosProvider);
      final hotState = ref.read(homeRecommendationHotProvider);

      // 如果国家已选择，分类数据已加载，但首页数据为空且不在加载中，则触发加载
      final shouldLoadData = countryState.selectedCountry != null &&
                            !countryState.isLoading &&
                            _categories.isNotEmpty &&
                            homeTabState.stations.isEmpty &&
                            !homeTabState.isLoading &&
                            hotState.stations.isEmpty &&
                            !hotState.isLoading;

      if (shouldLoadData) {
        print('🏠 Build时检测到需要加载数据，触发数据加载');
        print('   - 国家: ${countryState.selectedCountry?.name}');
        print('   - 分类数量: ${_categories.length}');
        print('   - 首页数据: ${homeTabState.stations.length}');
        print('   - 推荐数据: ${hotState.stations.length}');
        _loadInitialData();
      }
    });
  }

  /// 显示缓存的首页数据
  void _displayCachedHomeData(StartupCacheData cachedData) {
    try {
      // 设置首页Tab数据
      final homeTabNotifier = ref.read(homeTabRadiosProvider.notifier);
      homeTabNotifier.state = RadioListState(
        stations: cachedData.homeStations,
        isLoading: false,
        hasMore: false,
        currentPage: 1,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: cachedData.selectedCountry.id,
          query: 'cached_home_data',
        ),
      );

      // 设置推荐数据 - 热门推荐
      final hotNotifier = ref.read(homeRecommendationHotProvider.notifier);
      hotNotifier.state = RadioListState(
        stations: cachedData.popularStations,
        isLoading: false,
        hasMore: false,
        currentPage: 1,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: cachedData.selectedCountry.id,
          query: 'cached_popular_data',
        ),
      );

      // 设置推荐数据 - 点击最高（使用专门的点击最高缓存数据）
      final mostClickedNotifier = ref.read(homeRecommendationMostClickedProvider.notifier);
      mostClickedNotifier.state = RadioListState(
        stations: cachedData.mostClickedStations,
        isLoading: false,
        hasMore: false,
        currentPage: 1,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: cachedData.selectedCountry.id,
          query: 'cached_most_clicked_data',
        ),
      );

      // 设置推荐数据 - 高质量（使用专门的高质量缓存数据）
      final highQualityNotifier = ref.read(homeRecommendationHighQualityProvider.notifier);
      highQualityNotifier.state = RadioListState(
        stations: cachedData.highQualityStations,
        isLoading: false,
        hasMore: false,
        currentPage: 1,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: cachedData.selectedCountry.id,
          query: 'cached_high_quality_data',
        ),
      );

      // 设置推荐数据 - 最新上架（使用专门的最新上架缓存数据）
      final latestNotifier = ref.read(homeRecommendationLatestProvider.notifier);
      latestNotifier.state = RadioListState(
        stations: cachedData.latestStations,
        isLoading: false,
        hasMore: false,
        currentPage: 1,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: cachedData.selectedCountry.id,
          query: 'cached_latest_data',
        ),
      );

      print('✅ 缓存数据已显示到UI');
    } catch (e) {
      print('❌ 显示缓存数据失败: $e');
    }
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_categories.isEmpty) {
        print('⏳ 分类列表为空，等待分类数据加载完成...');
        // 不直接返回，而是设置一个标志，等待分类数据加载完成后再触发
        _shouldLoadDataWhenCategoriesReady = true;
        return;
      }

      _selectedCategoryIndex = 0;

      // 检查是否需要加载数据
      final homeTabState = ref.read(homeTabRadiosProvider);
      final hotState = ref.read(homeRecommendationHotProvider);

      // 如果没有缓存数据或者数据为空，则加载数据
      if (!_cacheDataLoaded || homeTabState.stations.isEmpty || hotState.stations.isEmpty) {
        print('🔄 加载首页数据（无缓存或数据为空）');
        _loadStationsForCategory(_categories[_selectedCategoryIndex]);
        // 传入当前选中分类的ID到推荐区域
        _loadAllRecommendations(tagIds: [_categories[_selectedCategoryIndex].id]);
        _animationController.forward();
        // 启动卡片动画
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _cardAnimationController.forward();
          }
        });

        // 首次数据加载完成后保存到缓存
        print('⏰ 设置延迟保存缓存任务（3秒后）');
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            print('⏰ 延迟保存缓存任务触发');
            _saveDataToCache();
          }
        });
      } else {
        // 有缓存数据，后台更新最新数据
        print('🔄 后台更新最新数据...');
        _loadStationsForCategory(_categories[_selectedCategoryIndex], forceRefresh: true);
        // 传入当前选中分类的ID到推荐区域，强制刷新
        _loadAllRecommendations(forceRefresh: true, tagIds: [_categories[_selectedCategoryIndex].id]);

        // 数据更新完成后保存到缓存
        _saveDataToCache();
      }

      _lastLoadTime = DateTime.now();
      _shouldLoadDataWhenCategoriesReady = false; // 重置标志
    });
  }

  /// 监听全局数据刷新通知
  void _listenToDataRefresh() {
    // 监听全局数据刷新通知（当国家切换时）
    ref.listen(dataRefreshNotifierProvider, (previous, next) {
      if (previous != next && next > 0) {
        // 延迟一点时间，确保国家选择器的状态已经更新
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            _refreshAllDataWithAnimation();
          }
        });
      }
    });
  }

  /// 带动画效果的全数据刷新（仅用于国家切换）
  void _refreshAllDataWithAnimation() {
    if (_categories.isEmpty) {
      print('❌ 分类列表为空，无法刷新数据');
      return;
    }

    print('🌍 国家切换，刷新所有数据');

    // 重置动画
    _cardAnimationController.reset();

    // 国家切换时需要刷新所有数据：Tab数据 + 推荐数据
    _loadStationsForCategory(_categories[_selectedCategoryIndex], forceRefresh: true);
    // 传入当前选中分类的ID到推荐区域，强制刷新
    _loadAllRecommendations(forceRefresh: true, tagIds: [_categories[_selectedCategoryIndex].id]);

    // 重新启动动画
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _cardAnimationController.forward();
      }
    });
    _lastLoadTime = DateTime.now();
  }

  /// 仅刷新Tab数据（用于Tab切换）
  void _refreshTabDataOnly() {
    if (_categories.isEmpty) {
      print('❌ 分类列表为空，无法刷新Tab数据');
      return;
    }

    print('🔄 仅刷新Tab数据');
    _loadStationsForCategory(_categories[_selectedCategoryIndex], forceRefresh: true);
  }

  /// 保存当前数据到启动缓存
  void _saveDataToCache() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final selectedCountry = ref.read(selectedCountryProvider);
        final homeTabState = ref.read(homeTabRadiosProvider);
        final hotState = ref.read(homeRecommendationHotProvider);
        final mostClickedState = ref.read(homeRecommendationMostClickedProvider);
        final highQualityState = ref.read(homeRecommendationHighQualityProvider);
        final latestState = ref.read(homeRecommendationLatestProvider);

        print('💾 准备保存数据到缓存:');
        print('   - 选择的国家: ${selectedCountry?.name}');
        print('   - 首页电台数量: ${homeTabState.stations.length}');
        print('   - 热门推荐数量: ${hotState.stations.length} (votes排序)');
        print('   - 点击最高数量: ${mostClickedState.stations.length} (click_count排序)');
        print('   - 高质量数量: ${highQualityState.stations.length} (bitrate排序)');
        print('   - 最新上架数量: ${latestState.stations.length} (created_at排序)');
        print('   - 分类数量: ${_categories.length}');

        if (selectedCountry != null &&
            homeTabState.stations.isNotEmpty &&
            hotState.stations.isNotEmpty &&
            mostClickedState.stations.isNotEmpty &&
            highQualityState.stations.isNotEmpty &&
            latestState.stations.isNotEmpty &&
            _categories.isNotEmpty) {

          print('💾 开始保存数据到启动缓存...');
          await ref.read(startupCacheProvider.notifier).saveHomeDataToCache(
            homeStations: homeTabState.stations,
            popularStations: hotState.stations,
            mostClickedStations: mostClickedState.stations,
            highQualityStations: highQualityState.stations,
            latestStations: latestState.stations,
            categories: _categories,
            selectedCountry: selectedCountry,
            selectedCategoryId: _categories.isNotEmpty && _selectedCategoryIndex < _categories.length 
                ? _categories[_selectedCategoryIndex].id 
                : null,
          );

          print('✅ 首页数据已保存到启动缓存');
        } else {
          print('⚠️ 数据不完整，无法保存到缓存:');
          print('   - 国家为空: ${selectedCountry == null}');
          print('   - 首页数据为空: ${homeTabState.stations.isEmpty}');
          print('   - 热门数据为空: ${hotState.stations.isEmpty}');
          print('   - 点击最高数据为空: ${mostClickedState.stations.isEmpty}');
          print('   - 高质量数据为空: ${highQualityState.stations.isEmpty}');
          print('   - 最新上架数据为空: ${latestState.stations.isEmpty}');
          print('   - 分类为空: ${_categories.isEmpty}');
        }
      } catch (e) {
        print('❌ 保存数据到缓存失败: $e');
      }
    });
  }

  Future<void> _loadStationsForCategory(RadioCategory category, {bool forceRefresh = false}) async {
    final selectedCountry = ref.read(selectedCountryProvider);
    // print('🔍 Tab切换加载分类: ${category.name} (ID: ${category.id}), 强制刷新: $forceRefresh');
    // 修改：使用tag_ids而不是keyword
    await ref.read(homeTabRadiosProvider.notifier).loadRadiosByCategory(
      [category.id], // 使用标签ID数组
      refresh: forceRefresh,
      countryId: selectedCountry?.id,
      forceRefresh: forceRefresh,
    );

    // 如果是强制刷新且不是缓存数据，保存到缓存
    if (forceRefresh && !_cacheDataLoaded) {
      _saveDataToCache();
    }
  }

  /// 加载首页推荐数据（使用独立Provider，支持按分类过滤）
  Future<void> _loadAllRecommendations({bool forceRefresh = false, List<int>? tagIds}) async {
    final selectedCountry = ref.read(selectedCountryProvider);
    print('🏠 加载首页推荐数据，强制刷新: $forceRefresh, 国家: ${selectedCountry?.name}, 分类: $tagIds');

    // 如果不是强制刷新，且推荐数据已经有内容，则跳过加载
    if (!forceRefresh) {
      final hotState = ref.read(homeRecommendationHotProvider);
      if (hotState.stations.isNotEmpty) {
        print('🏠 推荐数据已存在，跳过重复加载');
        return;
      }
    }

    print('🏠 推荐区域数据源分配（按分类过滤）:');
    print('   - 热门推荐: getPopularRadios() API (sortBy: votes, tagIds: $tagIds)');
    print('   - 点击最高: getRadiosByCountry() API (sortBy: click_count, tagIds: $tagIds)');
    print('   - 高质量: getRadioList() API (sortBy: bitrate, tagIds: $tagIds)');
    print('   - 最新上架: getLatestRadios() API (sortBy: created_at, tagIds: $tagIds)');

    // 并行加载所有推荐数据 - 使用完全不同的API确保数据差异化，并支持分类过滤
    await Future.wait([
      // 热门推荐：使用热门电台API，支持分类过滤
      ref.read(homeRecommendationHotProvider.notifier).loadPopularRadios(
        refresh: forceRefresh,
        countryId: selectedCountry?.id,
        forceRefresh: forceRefresh,
        tagIds: tagIds,
      ),
      // 点击最高：使用按国家获取电台API，按点击数排序，支持分类过滤
      ref.read(homeRecommendationMostClickedProvider.notifier).loadRadiosByCountry(
        countryId: selectedCountry?.id ?? 1, // 使用当前选择的国家ID
        refresh: forceRefresh,
        forceRefresh: forceRefresh,
        sortBy: 'click_count', // 按点击数排序，与热门推荐(votes)区分
        sortOrder: 'desc',
        tagIds: tagIds,
      ),
      // 高质量电台：使用比特率排序获取高质量电台，支持分类过滤
      ref.read(homeRecommendationHighQualityProvider.notifier).loadHighQualityRadios(
        refresh: forceRefresh,
        countryId: selectedCountry?.id,
        forceRefresh: forceRefresh,
        tagIds: tagIds,
      ),
      // 最新上架：使用最新电台API，支持分类过滤
      ref.read(homeRecommendationLatestProvider.notifier).loadLatestRadios(
        refresh: forceRefresh,
        countryId: selectedCountry?.id,
        forceRefresh: forceRefresh,
        tagIds: tagIds,
      ),
    ]);

    // 如果是强制刷新且不是缓存数据，保存到缓存
    if (forceRefresh && !_cacheDataLoaded) {
      _saveDataToCache();
    }

    // 首次推荐数据加载完成后也保存到缓存
    if (!forceRefresh && !_cacheDataLoaded) {
      print('⏰ 推荐数据加载完成，设置延迟保存缓存任务（2秒后）');
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          print('⏰ 推荐数据延迟保存缓存任务触发');
          _saveDataToCache();
        }
      });
    }
  }

  void _onCategorySelected(int index) {
    if (_categories.isEmpty || index >= _categories.length) {
      print('❌ 无效的分类索引: $index, 分类数量: ${_categories.length}');
      return;
    }

    if (_selectedCategoryIndex != index) {
      print('🔄 Tab切换: ${_categories[_selectedCategoryIndex].name} -> ${_categories[index].name}');

      // 优化：UI立即响应，提升用户体验
      setState(() {
        _selectedCategoryIndex = index;
      });

      // 新需求：Tab切换时，推荐区域也要按新分类过滤
      _loadStationsForCategoryAsync(_categories[index]);
      // 重新加载推荐区域，使用异步缓存检查方法
      _loadAllRecommendationsAsync(tagIds: [_categories[index].id]);

      // 优化：tab切换时不重置动画，避免分批显示问题
      // 只在首次加载时播放动画，tab切换时保持动画完成状态
      if (_cardAnimationController.value == 0.0) {
        _cardAnimationController.forward();
      }
    }
  }

  /// 异步加载分类电台数据，优化用户体验
  Future<void> _loadStationsForCategoryAsync(RadioCategory category) async {
    // 先检查是否有缓存数据，如果有则立即显示
    final selectedCountry = ref.read(selectedCountryProvider);
    final currentState = ref.read(homeTabRadiosProvider);

    // 如果有有效缓存，直接返回，避免loading状态
    if (currentState.isCacheValid(countryId: selectedCountry?.id, query: category.id.toString())) {
      print('🚀 使用缓存数据，Tab切换无延迟: ${category.name}');
      return;
    }

    // 异步加载新数据
    await _loadStationsForCategory(category, forceRefresh: false);
  }

  /// 异步加载推荐数据，优先使用预加载缓存（模仿网格区域实现）
  Future<void> _loadAllRecommendationsAsync({List<int>? tagIds}) async {
    if (tagIds == null || tagIds.isEmpty) {
      print('⚠️ tagIds为空，无法使用预加载缓存');
      await _loadAllRecommendations(forceRefresh: false, tagIds: tagIds);
      return;
    }

    final categoryId = tagIds.first.toString();
    final preloadCacheNotifier = ref.read(recommendationPreloadCacheProvider.notifier);

    // 检查四种推荐类型的预加载缓存
    final hotCachedData = preloadCacheNotifier.getRecommendationData(categoryId, 'hot');
    final mostClickedCachedData = preloadCacheNotifier.getRecommendationData(categoryId, 'most_clicked');
    final highQualityCachedData = preloadCacheNotifier.getRecommendationData(categoryId, 'high_quality');
    final latestCachedData = preloadCacheNotifier.getRecommendationData(categoryId, 'latest');

    // 检查缓存是否全部存在
    final hasAllCache = hotCachedData != null && hotCachedData.stations.isNotEmpty &&
                       mostClickedCachedData != null && mostClickedCachedData.stations.isNotEmpty &&
                       highQualityCachedData != null && highQualityCachedData.stations.isNotEmpty &&
                       latestCachedData != null && latestCachedData.stations.isNotEmpty;

    if (hasAllCache) {
      print('🚀 使用推荐预加载缓存，Tab切换无延迟，分类: $categoryId');
      print('   - 热门推荐: ${hotCachedData!.stations.length}个电台');
      print('   - 点击最高: ${mostClickedCachedData!.stations.length}个电台');
      print('   - 高质量: ${highQualityCachedData!.stations.length}个电台');
      print('   - 最新上架: ${latestCachedData!.stations.length}个电台');

      // 直接设置各个Provider的状态，模仿网格区域的做法
      ref.read(homeRecommendationHotProvider.notifier).state = hotCachedData;
      ref.read(homeRecommendationMostClickedProvider.notifier).state = mostClickedCachedData;
      ref.read(homeRecommendationHighQualityProvider.notifier).state = highQualityCachedData;
      ref.read(homeRecommendationLatestProvider.notifier).state = latestCachedData;
      
      return;
    }
    
    print('🔄 推荐预加载缓存不完整，异步加载:');
    print('   - 热门推荐缓存: ${hotCachedData != null && hotCachedData.stations.isNotEmpty}');
    print('   - 点击最高缓存: ${mostClickedCachedData != null && mostClickedCachedData.stations.isNotEmpty}');
    print('   - 高质量缓存: ${highQualityCachedData != null && highQualityCachedData.stations.isNotEmpty}');
    print('   - 最新上架缓存: ${latestCachedData != null && latestCachedData.stations.isNotEmpty}');
    
    // 如果没有完整缓存，回退到原有逻辑
    await _loadAllRecommendations(forceRefresh: false, tagIds: tagIds);
  }

  /// 验证图片URL是否有效
  bool _isValidImageUrl(String url) {
    if (url.isEmpty) return false;
    
    // 检查是否是有效的HTTP/HTTPS URL
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }
    
    // 检查是否包含图片文件扩展名
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    final path = uri.path.toLowerCase();
    final hasImageExtension = path.endsWith('.png') || 
                             path.endsWith('.jpg') || 
                             path.endsWith('.jpeg') || 
                             path.endsWith('.gif') || 
                             path.endsWith('.ico') || 
                             path.endsWith('.svg') ||
                             path.contains('favicon') ||
                             path.contains('logo') ||
                             path.contains('icon');
    
    // 检查是否是已知的图片服务域名
    final host = uri.host.toLowerCase();
    final isImageService = host.contains('githubusercontent.com') ||
                          host.contains('imgur.com') ||
                          host.contains('cloudinary.com') ||
                          host.contains('amazonaws.com') ||
                          host.contains('googleusercontent.com') ||
                          host.contains('iheart.com') ||
                          host.contains('radiobrowser.info');
    
    // 排除已知的问题URL模式
    final isProblematic = url.contains('duckduckgo.com') ||
                         url.contains('data:image') ||
                         url.length > 500; // 过长的URL通常有问题
    
    return (hasImageExtension || isImageService) && !isProblematic;
  }

  /// 过滤电台，只显示有有效图片的（优化版本，减少过度过滤 + URL验证）
  List<StationSimple> _filterStationsWithImage(List<StationSimple> stations) {
    final originalCount = stations.length;
    
    // 统计不同类型的图片URL
    int validFaviconCount = 0;
    int invalidFaviconCount = 0;
    int emptyFaviconCount = 0;
    
    // 优化过滤策略：保留更多电台
    final filtered = stations.where((s) {
      // 1. 优先保留有有效favicon的电台
      if (_isValidImageUrl(s.favicon)) {
        validFaviconCount++;
        return true;
      }
      
      // 2. 统计无效或空的favicon
      if (s.favicon.isEmpty) {
        emptyFaviconCount++;
      } else {
        invalidFaviconCount++;
      }
      
      // 3. 即使没有有效favicon，如果电台信息完整也保留一部分
      return s.name.isNotEmpty && s.url.isNotEmpty && s.country.isNotEmpty;
    }).toList();
    
    final filteredCount = filtered.length;
    final filterRate = originalCount > 0 ? ((originalCount - filteredCount) / originalCount * 100).toInt() : 0;
    
    print('🎯 数据过滤统计: 原始${originalCount}个 → 过滤后${filteredCount}个 (过滤率: ${filterRate}%)');
    print('📷 图片URL统计: 有效${validFaviconCount}个, 无效${invalidFaviconCount}个, 空白${emptyFaviconCount}个');
    print('📷 图片策略: 有效favicon优先显示，无效/空白favicon使用${BackupRadioImages.getBackupImageCount()}张备用图片');
    
    // 如果过滤后数据太少（少于原来的30%），则放宽条件
    if (filteredCount < originalCount * 0.3) {
      print('⚠️ 过滤后数据过少，放宽过滤条件');
      final relaxedFiltered = stations.where((s) => 
        s.name.isNotEmpty && 
        s.url.isNotEmpty && 
        s.url.startsWith('http')
      ).toList();
      
      final relaxedCount = relaxedFiltered.length;
      print('🔄 放宽条件后: ${relaxedCount}个电台');
      return relaxedFiltered;
    }
    
    return filtered;
  }

  void _playStation(StationSimple station) {
    final audioService = ref.read(audioServiceProvider);
    audioService.playStation(station);
    
    // 去掉播放成功的弹出信息
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(
    //     content: Text('playing_now'.tr(namedArgs: {'station': station.name})),
    //     duration: const Duration(seconds: 2),
    //     behavior: SnackBarBehavior.floating,
    //     shape: RoundedRectangleBorder(
    //       borderRadius: BorderRadius.circular(12),
    //     ),
    //   ),
    // );
  }

  /// 带动画效果的播放方法
  void _playStationWithAnimation(StationSimple station) {
    // 缩放动画
    _slideAnimationController.forward().then((_) {
      _slideAnimationController.reverse();
    });
    
    // 调用原始播放方法
    _playStation(station);
  }

  /// 带播放列表上下文的播放方法
  /// 
  /// 功能实现: 支持播放列表上下文的电台播放
  /// 实现方案: 构建播放列表上下文并调用AudioService的播放列表播放方法
  /// 影响范围: home_page.dart
  /// 实现日期: 2025-01-27
  void _playStationWithPlaylist({
    required StationSimple station,
    required List<StationSimple> stations,
    required PlaylistSourceType sourceType,
    required String sourceTitle,
  }) {
    // 找到当前电台在列表中的索引
    final currentIndex = stations.indexWhere((s) => s.id == station.id);
    if (currentIndex == -1) {
      print('❌ 无法在播放列表中找到当前电台: ${station.name}');
      // 如果找不到，降级为普通播放
      _playStation(station);
      return;
    }

    // 构建播放列表上下文
    final playlistContext = PlaylistContext(
      sourceType: sourceType,
      stations: stations,
      currentIndex: currentIndex,
      sourceTitle: sourceTitle,
    );

    print('🎵 播放电台（带播放列表）: ${station.name}');
    print('📋 播放列表: $sourceTitle (${currentIndex + 1}/${stations.length})');

    // 调用AudioService的播放列表播放方法
    final audioService = ref.read(audioServiceProvider);
    audioService.playStationWithPlaylist(
      station: station,
      playlistContext: playlistContext,
    );
  }

  /// 播放网格区域电台（带播放列表）
  void _playGridStationWithPlaylist(StationSimple station) {
    final categoryState = ref.read(homeTabRadiosProvider);
    final stations = categoryState.stations;
    
    if (stations.isEmpty) {
      _playStation(station);
      return;
    }
    
    // 只取当前显示的电台作为播放列表
    const rowCount = 4;
    const stationsPerRow = 18;
    final maxStations = rowCount * stationsPerRow;
    final displayStations = stations.take(maxStations).toList();
    
    String sourceTitle = 'category_stations'.tr();
    if (_categories.isNotEmpty && _selectedCategoryIndex < _categories.length) {
      sourceTitle = '${_categories[_selectedCategoryIndex].getLocalizedDisplayName()} ${'category_stations'.tr()}';
    }

    _playStationWithPlaylist(
      station: station,
      stations: displayStations,
      sourceType: PlaylistSourceType.categoryGrid,
      sourceTitle: sourceTitle,
    );
    
    // 添加动画效果
    _slideAnimationController.forward().then((_) {
      _slideAnimationController.reverse();
    });
  }

  /// 播放推荐区域电台（带播放列表）
  void _playRecommendationStationWithPlaylist(
    StationSimple station,
    PlaylistSourceType sourceType,
    List<StationSimple> stations,
    String sourceTitle,
  ) {
    // 只取显示的电台作为播放列表（通常是前20个）
    const maxCount = 20;
    final displayStations = stations.take(maxCount).toList();
    
    _playStationWithPlaylist(
      station: station,
      stations: displayStations,
      sourceType: sourceType,
      sourceTitle: sourceTitle,
    );
    
    // 添加动画效果
    _slideAnimationController.forward().then((_) {
      _slideAnimationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于AutomaticKeepAliveClientMixin

    // 启动全局数据刷新监听
    _listenToDataRefresh();

    // 监听动态分类数据（使用StateNotifier）
    final categoriesState = ref.watch(dynamicCategoriesProvider);

    // 首次加载分类数据
    if (categoriesState.categories.isEmpty && !categoriesState.isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(dynamicCategoriesProvider.notifier).loadCategories();
      });
    }

    // 分类数据加载完成后，触发预加载和推荐数据初始化
    if (categoriesState.categories.isNotEmpty) {
      final selectedCountry = ref.read(selectedCountryProvider);
      final preloadState = ref.watch(preloadCacheProvider);

      // 更严格的预加载触发条件，避免重复触发
      final shouldTriggerPreload = !preloadState.isPreloading &&
          preloadState.categoryCache.isEmpty &&
          preloadState.currentCountryId != selectedCountry?.id;

      if (shouldTriggerPreload) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          print('🚀 触发预加载，国家: ${selectedCountry?.name}');
          ref.read(preloadCacheProvider.notifier)
             .preloadAllCategories(categoriesState.categories, selectedCountry?.id);
          // 同时触发推荐预加载
          ref.read(recommendationPreloadCacheProvider.notifier)
             .preloadAllRecommendations(categoriesState.categories, selectedCountry?.id);
        });
      }

      // 推荐数据初始化（只在首次加载时执行，且只执行一次）
      // 确保国家已选择且分类数据已加载完成
      if (!_recommendationsInitialized && selectedCountry != null) {
        _recommendationsInitialized = true;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          print('🏠 首次初始化推荐数据（仅执行一次），国家: ${selectedCountry.name}');
          // 如果已经有选中的分类，使用分类ID，否则使用空
          final tagIds = _categories.isNotEmpty && _selectedCategoryIndex < _categories.length 
              ? [_categories[_selectedCategoryIndex].id] 
              : null;
          _loadAllRecommendations(tagIds: tagIds);
        });
      }
    }

    // 如果正在加载或有错误，显示相应状态
    if (categoriesState.isLoading && categoriesState.categories.isEmpty) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (categoriesState.error != null && categoriesState.categories.isEmpty) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('加载分类失败: ${categoriesState.error}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.read(dynamicCategoriesProvider.notifier).loadCategories(forceRefresh: true);
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    final categories = categoriesState.categories;

    // 更新本地分类数据
    if (_categories != categories) {
      final wasEmpty = _categories.isEmpty;
      _categories = categories;
      // 如果当前选中的索引超出范围，重置为0
      if (_selectedCategoryIndex >= _categories.length) {
        _selectedCategoryIndex = 0;
      }

      // 如果之前分类为空，现在有数据了，且需要加载数据，则触发数据加载
      if (wasEmpty && _categories.isNotEmpty && _shouldLoadDataWhenCategoriesReady) {
        print('🏠 分类数据加载完成，触发延迟的数据加载');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _loadInitialData();
        });
      }

      // 额外检查：如果分类数据已加载但首页数据为空，也触发加载
      if (_categories.isNotEmpty && !_shouldLoadDataWhenCategoriesReady) {
        final homeTabState = ref.read(homeTabRadiosProvider);
        final hotState = ref.read(homeRecommendationHotProvider);
        final countryState = ref.read(countrySelectionProvider);

        if (countryState.selectedCountry != null &&
            homeTabState.stations.isEmpty &&
            hotState.stations.isEmpty &&
            !homeTabState.isLoading &&
            !hotState.isLoading) {
          print('🏠 分类数据已存在但首页数据为空，触发数据加载');
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _loadInitialData();
          });
        }
      }
    }

    // 每次build时检查是否需要加载数据（防止错过初始化时机）
    _checkAndLoadDataOnBuild();

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: _buildDynamicGradient(),
        ),
        child: Column(
          children: [
            // 固定顶部Tab区域（不滚动）- 与主页面统一的背景色系
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF0F0F23).withOpacity(0.95), // 深蓝黑
                    const Color(0xFF1A1A2E).withOpacity(0.90), // 深紫蓝
                    const Color(0xFF16213E).withOpacity(0.85), // 深蓝
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
                border: Border(
                  bottom: BorderSide(
                    color: Colors.white.withOpacity(0.15),
                    width: 0.5,
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    offset: const Offset(0, 2),
                    blurRadius: 15,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: SafeArea(
                bottom: false,
                child: _buildModernTabSection(),
              ),
            ),

            // 可滚动内容区域
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  print('🔄 用户手动下拉刷新 - 刷新所有数据');

                  // 清空预加载缓存
                  ref.read(preloadCacheProvider.notifier).clearCache();

                  // 刷新分类数据
                  await ref.read(dynamicCategoriesProvider.notifier).loadCategories(forceRefresh: true);

                  if (_categories.isNotEmpty) {
                    // 重新触发预加载
                    final selectedCountry = ref.read(selectedCountryProvider);
                    ref.read(preloadCacheProvider.notifier)
                       .preloadAllCategories(_categories, selectedCountry?.id);

                    // 触发推荐预加载
                    ref.read(recommendationPreloadCacheProvider.notifier)
                       .preloadAllRecommendations(_categories, selectedCountry?.id);

                    // 手动刷新时需要刷新所有数据：Tab数据 + 推荐数据
                    await Future.wait([
                      _loadStationsForCategory(_categories[_selectedCategoryIndex], forceRefresh: true),
                      // 传入当前选中分类的ID到推荐区域，强制刷新
                      _loadAllRecommendations(forceRefresh: true, tagIds: [_categories[_selectedCategoryIndex].id]),
                      ]);
                  }
                  _lastLoadTime = DateTime.now();
                  print('✅ 手动刷新完成');
                },
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics(),
                  ),
                  slivers: [
                    // 顶部间距（之前Tab区域的间距）
                    const SliverToBoxAdapter(
                      child: SizedBox(height: 16),
                    ),

                    // 独立推荐区域（按选中分类过滤）
                    SliverToBoxAdapter(
                      child: _buildRecommendationsSection(),
                    ),

                    // 分隔推荐区域和分类电台
                    const SliverToBoxAdapter(
                      child: SizedBox(height: 32),
                    ),

                    // Tab对应的电台网格区 - 分类电台列表
                    SliverToBoxAdapter(
                      child: _buildTabStationsSection(),
                    ),

                    // 底部安全区域 - 为MiniPlayer预留空间
                    const SliverToBoxAdapter(
                      child: SizedBox(height: 100),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 构建Tab对应的电台网格区
  Widget _buildTabStationsSection() {
    final categoryState = ref.watch(homeTabRadiosProvider);

    // 如果有新数据，更新保存的数据
    if (categoryState.stations.isNotEmpty) {
      _lastStations = categoryState.stations;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分类电台标题
          if (_categories.isNotEmpty && _selectedCategoryIndex < _categories.length)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
              child: Text(
                '${_categories[_selectedCategoryIndex].getLocalizedDisplayName()} ${'category_stations'.tr()}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),

          // 电台网格内容
          categoryState.isLoading && categoryState.stations.isEmpty
              ? (_lastStations.isNotEmpty
                  ? _buildStationsGrid(_lastStations) // 显示上一次的数据
                  : _buildSkeletonGrid()) // 使用骨架屏而不是loading圆圈
              : categoryState.error != null
                  ? Container(
                      height: 200,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error_outline, color: Colors.red[400], size: 32),
                            const SizedBox(height: 8),
                                                          Text('load_failed'.tr()),
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: () {
                                if (_categories.isNotEmpty && _selectedCategoryIndex < _categories.length) {
                                  _loadStationsForCategory(_categories[_selectedCategoryIndex]);
                                }
                              },
                                                              child: Text('retry'.tr()),
                            ),
                          ],
                        ),
                      ),
                    )
                  : categoryState.stations.isEmpty
                      ? (_lastStations.isNotEmpty
                          ? _buildStationsGrid(_lastStations) // 显示上一次的数据
                          : Container(
                              height: 200,
                              child: Center(child: Text('no_data'.tr())),
                            ))
                      : _buildStationsGrid(categoryState.stations),
        ],
      ),
    );
  }

  /// 构建电台网格（优化显示数量，调整为4行布局，尺寸增加80%）
  Widget _buildStationsGrid(List<StationSimple> stations) {
    const rowCount = 4; // 修改为4行
            const stationsPerRow = 18; // 由于尺寸缩小，增加每行数量
          final maxStations = rowCount * stationsPerRow; // 72个电台

          // 取前72个电台
    final displayStations = stations.take(maxStations).toList();
    print('🎯 网格显示: 最多显示${maxStations}个电台，实际显示${displayStations.length}个');
    
    return Column(
      children: List.generate(rowCount, (rowIndex) {
        final startIndex = rowIndex * stationsPerRow;
        final endIndex = (startIndex + stationsPerRow).clamp(0, displayStations.length);
        
        if (startIndex >= displayStations.length) {
          return const SizedBox();
        }
        
        final rowStations = displayStations.sublist(startIndex, endIndex);
        
        return Column(
          children: [
            SizedBox(
              height: 138, // 与推荐区域保持一致: 78(图片) + 12(padding) + 48(文本) = 138
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                itemCount: rowStations.length,
                itemBuilder: (context, index) {
                  final station = rowStations[index];
                  final globalIndex = rowIndex * stationsPerRow + index;
                  return Container(
                    width: 98, // 缩小80%: 122 * 0.8 = 98
                    margin: const EdgeInsets.only(right: 7.4), // 缩小80%: 9.2 * 0.8 = 7.4
                    child: _buildGridStationCard(station, globalIndex),
                  );
                },
              ),
            ),
            if (rowIndex < rowCount - 1) const SizedBox(height: 14), // 增加80%: 8 * 1.8 = 14
          ],
        );
      }),
    );
  }

  /// 构建网格区域的电台卡片（使用StationCard组件）
  Widget _buildGridStationCard(StationSimple station, int index) {
    final cardKey = '${station.id}_$index';
    _cardKeys[cardKey] ??= GlobalKey();
    
    return AnimatedBuilder(
      animation: _cardAnimationController,
      builder: (context, child) {
        // 简化动画：所有卡片同时显示，避免分批显示问题
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0, 0.1), // 轻微滑动效果
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _cardAnimationController,
          curve: Curves.easeOutCubic,
        ));

        final fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _cardAnimationController,
          curve: Curves.easeIn,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.95, // 轻微缩放效果
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _cardAnimationController,
          curve: Curves.easeOut,
        ));
        
        return FadeTransition(
          opacity: fadeAnimation,
          child: SlideTransition(
            position: slideAnimation,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: StationCard(
                station: station,
                width: 98, // 缩小80%: 122 * 0.8 = 98
                imageHeight: 78, // 与推荐区域保持一致: 78
                borderRadius: 10, // 与推荐区域保持一致: 10
                margin: EdgeInsets.zero, // 外部Container已处理margin
                padding: const EdgeInsets.all(6.0), // 与推荐区域保持一致: 6.0
                showCountry: false, // 网格区域不显示国家信息
                maxNameLines: 2,
                style: StationCardStyle.glassmorphism,
                onTap: () => _playGridStationWithPlaylist(station),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建推荐区域的电台卡片（使用StationCard组件）
  Widget _buildRecommendationStationCard(StationSimple station, int index) {
    return StationCard(
      station: station,
      width: 98, // 缩小80%: 122 * 0.8 = 98  
      imageHeight: 78, // 推荐区域图片高度，缩小80%: 98 * 0.8 = 78
      borderRadius: 10,
      margin: EdgeInsets.zero, // 外部Container已处理margin
      padding: const EdgeInsets.all(6.0), // 推荐区域padding
      showCountry: false, // 推荐区域不显示国家信息
      maxNameLines: 2,
      style: StationCardStyle.glassmorphism,
      onTap: () {
        // 根据当前推荐区域类型确定播放列表来源
        // 这里需要传递当前推荐区域的电台列表和类型
        // 由于这个方法被多个推荐区域复用，暂时使用普通播放
        _playStationWithAnimation(station);
      },
    );
  }

  /// 构建支持播放列表的推荐区域电台卡片
  /// 
  /// 功能实现: 构建带播放列表上下文的推荐区域电台卡片
  /// 实现方案: 基于StationCard组件，传递播放列表播放逻辑
  /// 影响范围: home_page.dart
  /// 实现日期: 2025-01-27
  Widget _buildRecommendationStationCardWithPlaylist(
    StationSimple station,
    int index,
    List<StationSimple> stations,
    PlaylistSourceType sourceType,
    String sourceTitle,
  ) {
    return StationCard(
      station: station,
      width: 98, // 缩小80%: 122 * 0.8 = 98  
      imageHeight: 78, // 推荐区域图片高度，缩小80%: 98 * 0.8 = 78
      borderRadius: 10,
      margin: EdgeInsets.zero, // 外部Container已处理margin
      padding: const EdgeInsets.all(6.0), // 推荐区域padding
      showCountry: false, // 推荐区域不显示国家信息
      maxNameLines: 2,
      style: StationCardStyle.glassmorphism,
      onTap: () => _playRecommendationStationWithPlaylist(
        station, 
        sourceType, 
        stations, 
        sourceTitle,
      ),
    );
  }



  /// 构建独立推荐区域
  Widget _buildRecommendationsSection() {
    return Column(
      children: [
        // 热门推荐 - 使用独立Provider
        _buildRecommendationRow(
          title: 'hot_recommendations'.tr(),
          provider: homeRecommendationHotProvider,
          maxCount: 20,
          sourceType: PlaylistSourceType.hotRecommendations,
        ),

        const SizedBox(height: 24),

        // 点击最高 - 使用独立Provider
        _buildRecommendationRow(
          title: 'most_clicked'.tr(),
          provider: homeRecommendationMostClickedProvider,
          maxCount: 20,
          sourceType: PlaylistSourceType.mostClicked,
        ),

        const SizedBox(height: 24),

        // 高质量 - 使用独立Provider
        _buildRecommendationRow(
          title: 'high_quality'.tr(),
          provider: homeRecommendationHighQualityProvider,
          maxCount: 20,
          sourceType: PlaylistSourceType.highQuality,
        ),

        const SizedBox(height: 24),

        // 最新上架 - 使用独立Provider
        _buildRecommendationRow(
          title: 'latest_added'.tr(),
          provider: homeRecommendationLatestProvider,
          maxCount: 15,
          sourceType: PlaylistSourceType.latestAdded,
        ),
      ],
    );
  }

  /// 构建单个推荐行
  Widget _buildRecommendationRow({
    required String title,
    required StateNotifierProvider<dynamic, RadioListState> provider,
    required int maxCount,
    required PlaylistSourceType sourceType,
  }) {
    final state = ref.watch(provider);

    // 调试日志：监控推荐区域的状态变化
    print('📊 推荐区域 $title: 数据量=${state.stations.length}, 加载中=${state.isLoading}, 初始化标志=$_recommendationsInitialized');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildNeonText(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                  glowColor: const Color(0xFF00FFFF),
                ),
                TextButton(
                  onPressed: () => context.go('/explore'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'more'.tr(),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 2),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.grey.shade600,
                        size: 12,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // 电台横向列表
          _buildRecommendStationsList(state, maxCount, sourceType, title),
        ],
      ),
    );
  }

  /// 构建推荐电台列表
  Widget _buildRecommendStationsList(
    RadioListState state, 
    int maxCount, 
    PlaylistSourceType sourceType, 
    String sourceTitle,
  ) {
    if (state.isLoading && state.stations.isEmpty) {
      return Container(
        height: 120,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (state.error != null && state.stations.isEmpty) {
      return Container(
        height: 120,
        child: Center(
          child: Text('load_failed'.tr(), style: TextStyle(color: Colors.red[400])),
        ),
      );
    }

    if (state.stations.isEmpty) {
      return Container(
        height: 120,
        child: Center(child: Text('no_data'.tr())),
      );
    }

    // 横向电台列表 - 只显示有图片的
    final stations = state.stations.take(maxCount).toList();
    
    return SizedBox(
      height: 138, // 缩小80%: 173 * 0.8 = 138
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 4.6), // 缩小80%: 5.8 * 0.8 = 4.6
        itemCount: stations.length,
        itemBuilder: (context, index) {
          final station = stations[index];
          return Container(
            width: 98, // 缩小80%: 122 * 0.8 = 98
            margin: const EdgeInsets.only(right: 9.2), // 缩小80%: 11.5 * 0.8 = 9.2
            child: _buildRecommendationStationCardWithPlaylist(
              station, 
              index + 100, 
              stations, 
              sourceType, 
              sourceTitle,
            ), // 使用支持播放列表的卡片构建方法
          );
        },
      ),
    );
  }

  /// 构建简单的骨架屏
  Widget _buildSkeletonGrid() {
    const rowCount = 4;
            const stationsPerRow = 18; // 增加数量，与实际网格一致

    return Column(
      children: List.generate(rowCount, (rowIndex) {
        return Column(
          children: [
            SizedBox(
              height: 138, // 与网格区域保持一致: 138
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                itemCount: stationsPerRow,
                itemBuilder: (context, index) {
                  return Container(
                    width: 98, // 缩小80%: 122 * 0.8 = 98，与网格区域保持一致
                    margin: const EdgeInsets.only(right: 7.4), // 缩小80%: 9.2 * 0.8 = 7.4，与网格区域保持一致
                    child: Column(
                      children: [
                        Container(
                          width: 86, // 缩小80%: 108 * 0.8 = 86
                          height: 78, // 与StationCard图片高度一致: 78
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(10), // 与StationCard边框半径一致
                          ),
                        ),
                        const SizedBox(height: 6.0), // 与StationCard padding一致
                        Container(
                          width: 72, // 缩小80%: 90 * 0.8 = 72
                          height: 17, // 缩小80%: 21.6 * 0.8 = 17
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            if (rowIndex < rowCount - 1) const SizedBox(height: 14), // 增加80%: 8 * 1.8 = 14
          ],
        );
      }),
    );
  }

  /// 构建动态渐变背景
  LinearGradient _buildDynamicGradient() {
    // 基于demo.png的深色渐变风格
    const primaryGradient = [
      Color(0xFF0F0F23), // 深蓝黑
      Color(0xFF1A1A2E), // 深紫蓝
      Color(0xFF16213E), // 深蓝
      Color(0xFF0F3460), // 中蓝
    ];

    // 使用动画控制器的值来创建动态效果
    final animationValue = _animationController.value;

    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: primaryGradient,
      stops: [
        0.0,
        0.3 + (animationValue * 0.2),
        0.6 + (animationValue * 0.2),
        1.0,
      ],
    );
  }

  /// 构建现代化Tab切换区
  Widget _buildModernTabSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: SizedBox(
        height: 40,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final isSelected = index == _selectedCategoryIndex;

            return Container(
              margin: const EdgeInsets.only(right: 12),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _onCategorySelected(index),
                  borderRadius: BorderRadius.circular(20),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOutCubic,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    decoration: _buildModernTabDecoration(isSelected),
                    child: Text(
                      category.getLocalizedDisplayName(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? Colors.white // 清晰的白色文字
                            : Colors.white.withOpacity(0.7),
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建现代化Tab装饰 - 科技感设计
  BoxDecoration _buildModernTabDecoration(bool isSelected) {
    if (isSelected) {
      // 选中状态：科技感渐变效果
      return BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF00FFFF).withOpacity(0.15),
            const Color(0xFF0080FF).withOpacity(0.12),
            const Color(0xFF0040FF).withOpacity(0.08),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
        border: Border.all(
          color: const Color(0xFF00FFFF).withOpacity(0.4),
          width: 1.5,
        ),
        boxShadow: [
          // 主阴影
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
          // 科技感高光
          BoxShadow(
            color: const Color(0xFF00FFFF).withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, -1),
            spreadRadius: 0,
          ),
        ],
      );
    } else {
      // 未选中状态：增强玻璃态效果
      return BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.12),
            Colors.white.withOpacity(0.06),
          ],
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.25),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.12),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      );
    }
  }

  /// 构建霓虹发光文本
  Widget _buildNeonText(
    String text, {
    required TextStyle style,
    Color glowColor = const Color(0xFF00FFFF),
    double glowIntensity = 0.6,
    int glowLayers = 2,
  }) {
    return Stack(
      children: [
        // 发光层
        for (int i = glowLayers; i > 0; i--)
          Text(
            text,
            style: style.copyWith(
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = i * 1.5
                ..color = glowColor.withOpacity(glowIntensity / (i * 1.5)),
            ),
          ),
        // 主文本
        Text(text, style: style),
      ],
    );
  }
}
